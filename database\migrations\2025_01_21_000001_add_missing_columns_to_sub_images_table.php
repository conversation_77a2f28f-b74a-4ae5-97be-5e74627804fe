<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToSubImagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sub_images', function (Blueprint $table) {
            // Add the missing columns that are expected by the SubImages model
            $table->string('Image')->nullable();
            $table->string('Product')->nullable(); // Foreign key to products
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sub_images', function (Blueprint $table) {
            // Remove the added columns
            $table->dropColumn(['Image', 'Product']);
        });
    }
}
