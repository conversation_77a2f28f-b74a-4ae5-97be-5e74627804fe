<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToAssemblyProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('assembly_products', function (Blueprint $table) {
            // Add all the missing columns that are expected by the AssemblyProducts model
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Total', 15, 2)->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('P_Code')->nullable();
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('p_id')->nullable(); // Foreign key to products (parent product)
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('assembly_products', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Qty', 'Price', 'Total', 'P_Ar_Name', 'P_En_Name', 'P_Code', 
                'Unit', 'Product', 'p_id'
            ]);
        });
    }
}
