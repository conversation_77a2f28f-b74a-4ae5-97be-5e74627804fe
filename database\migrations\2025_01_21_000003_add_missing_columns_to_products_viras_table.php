<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsVirasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_viras', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsVira model
            $table->decimal('Cost', 15, 2)->nullable();
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_viras', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn(['Cost', 'Product', 'V1', 'V2']);
        });
    }
}
