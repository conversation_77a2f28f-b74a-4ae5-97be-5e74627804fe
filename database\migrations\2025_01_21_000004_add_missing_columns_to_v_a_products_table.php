<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToVAProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('v_a_products', function (Blueprint $table) {
            // Add all the missing columns that are expected by the VAProducts model
            $table->string('Product_Code')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('ProductID')->nullable(); // Foreign key to products
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('v_a_products', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Product_Code', 'P_Ar_Name', 'P_En_Name', 'Unit', 'Product', 'ProductID'
            ]);
        });
    }
}
