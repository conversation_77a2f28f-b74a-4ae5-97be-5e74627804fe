<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToVAQtiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('v_a_qties', function (Blueprint $table) {
            // Add all the missing columns that are expected by the VAQty model
            $table->decimal('Qty', 15, 4)->nullable();
            $table->string('MainV')->nullable(); // Foreign key to virables
            $table->string('SubV')->nullable(); // Foreign key to sub_virables
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('ProductID')->nullable(); // Foreign key to products
            $table->string('VAProductID')->nullable(); // Foreign key to v_a_products
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('v_a_qties', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Qty', 'MainV', 'SubV', 'Product', 'ProductID', 'VAProductID'
            ]);
        });
    }
}
