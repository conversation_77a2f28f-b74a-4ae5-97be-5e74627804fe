<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToVAPricesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('v_a_prices', function (Blueprint $table) {
            // Add all the missing columns that are expected by the VAPrice model
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Offer_Price', 15, 2)->nullable();
            $table->string('MainV')->nullable(); // Foreign key to virables
            $table->string('SubV')->nullable(); // Foreign key to sub_virables
            $table->string('Product')->nullable(); // Foreign key to products
            $table->string('Added')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('v_a_prices', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Price', 'Offer_Price', 'MainV', 'SubV', 'Product', 'Added'
            ]);
        });
    }
}
