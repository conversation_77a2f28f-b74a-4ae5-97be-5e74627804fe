<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToAdditionalProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('additional_products', function (Blueprint $table) {
            // Add all the missing columns that are expected by the AdditionalProducts model
            $table->string('Additional_Product')->nullable(); // Foreign key to products
            $table->string('Product')->nullable(); // Foreign key to products
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('additional_products', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn(['Additional_Product', 'Product']);
        });
    }
}
