<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToProductsPermissionToRecivedGoodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products_permission_to_recived_goods', function (Blueprint $table) {
            // Add all the missing columns that are expected by the ProductsPermissionToRecivedGoods model
            $table->string('Product_Code')->nullable();
            $table->string('P_Ar_Name')->nullable();
            $table->string('P_En_Name')->nullable();
            $table->string('V_Name')->nullable();
            $table->string('VV_Name')->nullable();
            $table->decimal('Original_Qty', 15, 4)->nullable();
            $table->decimal('AvQty', 15, 4)->nullable();
            $table->decimal('Qty', 15, 4)->nullable();
            $table->decimal('Price', 15, 2)->nullable();
            $table->decimal('Discount', 15, 2)->nullable();
            $table->string('Tax')->nullable(); // Foreign key to taxes
            $table->decimal('Total_Bf_Tax', 15, 2)->nullable();
            $table->decimal('Total_Tax', 15, 2)->nullable();
            $table->decimal('Total', 15, 2)->nullable();
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('To_Store')->nullable(); // Foreign key to stores
            $table->date('Date')->nullable();
            $table->string('Product')->nullable(); // Foreign key to products
            $table->date('Exp_Date')->nullable();
            $table->string('V1')->nullable(); // Foreign key to sub_virables
            $table->string('V2')->nullable(); // Foreign key to sub_virables
            $table->string('Unit')->nullable(); // Foreign key to measuerments
            $table->string('Recived')->nullable(); // Foreign key to permission_to_recived_goods
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products_permission_to_recived_goods', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Product_Code', 'P_Ar_Name', 'P_En_Name', 'V_Name', 'VV_Name', 'Original_Qty',
                'AvQty', 'Qty', 'Price', 'Discount', 'Tax', 'Total_Bf_Tax', 'Total_Tax', 'Total',
                'Store', 'To_Store', 'Date', 'Product', 'Exp_Date', 'V1', 'V2', 'Unit', 'Recived'
            ]);
        });
    }
}
