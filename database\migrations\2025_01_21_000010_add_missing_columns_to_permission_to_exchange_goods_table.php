<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToPermissionToExchangeGoodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('permission_to_exchange_goods', function (Blueprint $table) {
            // Add all the missing columns that are expected by the PermissionToExchangeGoods model
            $table->string('Code')->nullable();
            $table->date('Date')->nullable();
            $table->string('Draw')->nullable();
            $table->integer('Product_Numbers')->nullable();
            $table->decimal('Total_Qty', 15, 4)->nullable();
            $table->decimal('Total_Discount', 15, 2)->nullable();
            $table->decimal('Total_BF_Taxes', 15, 2)->nullable();
            $table->decimal('Total_Taxes', 15, 2)->nullable();
            $table->decimal('Total_Price', 15, 2)->nullable();
            $table->text('Note')->nullable();
            $table->string('Account')->nullable(); // Foreign key to acccounting_manuals
            $table->string('Store')->nullable(); // Foreign key to stores
            $table->string('To_Store')->nullable(); // Foreign key to stores
            $table->string('Coin')->nullable(); // Foreign key to coins
            $table->string('Cost_Center')->nullable(); // Foreign key to cost_centers
            $table->string('User')->nullable(); // Foreign key to admins
            $table->string('Status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('permission_to_exchange_goods', function (Blueprint $table) {
            // Remove all the added columns
            $table->dropColumn([
                'Code', 'Date', 'Draw', 'Product_Numbers', 'Total_Qty', 'Total_Discount',
                'Total_BF_Taxes', 'Total_Taxes', 'Total_Price', 'Note', 'Account', 'Store',
                'To_Store', 'Coin', 'Cost_Center', 'User', 'Status'
            ]);
        });
    }
}
