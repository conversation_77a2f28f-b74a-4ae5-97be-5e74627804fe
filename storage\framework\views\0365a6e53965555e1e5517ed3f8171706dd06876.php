    <?php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>   
      <div class="page-content-overlay" data-action="toggle" data-class="mobile-nav-on"></div>
               <!-- END Page Content -->
               <!-- BEGIN Page Footer -->
               <footer class="page-footer" role="contentinfo">
                  <div class="d-flex align-items-center flex-1 text-muted">
                     <span class="hidden-md-down fw-700"><?php echo e(date('Y')); ?> © OST ERP by&nbsp;<a href='https://www.ostegy.com' class='text-primary fw-500' title='ostegy.com' target='_blank'>ostegy.com</a></span>
                  </div>
   
               </footer>
               <!-- END Page Footer -->

<script src="<?php echo e(asset('Admin/js/vendors.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/app.bundle.js')); ?>"></script>
      <script type="text/javascript">
         /* Activate smart panels */
         $('#js-page-content').smartPanel();
         
      </script>
      <!-- The order of scripts is irrelevant. Please check out the plugin pages for more details about these plugins below: -->
      <script src="<?php echo e(asset('Admin/js/dependency/moment/moment.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/miscellaneous/fullcalendar/fullcalendar.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/statistics/sparkline/sparkline.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/statistics/easypiechart/easypiechart.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/statistics/flot/flot.bundle.js')); ?>"></script>
      <script src="<?php echo e(asset('Admin/js/miscellaneous/jqvmap/jqvmap.bundle.js')); ?>"></script>
  
    <script>
$(document).ready(function(){
  setTimeout(function(){ $("#ex").hide(); }, 6000);
});
</script>


<script>
    $(document).ready(function(){
var myDivObj =$('.page-sidebar').css("background-color"); 

var m=$('#inpu').val();
var m1=$('#inpu1').val();
var m2=$('#inpu2').val();
var m3=$('#inpu3').val();
var m4=$('#inpu4').val();
var m5=$('#inpu5').val();
var m6=$('#inpu6').val();
var m7=$('#inpu7').val();
var m8=$('#inpu8').val();
var m9=$('#inpu9').val();
var m10=$('#inpu10').val();
var m11=$('#inpu11').val();
var m12=$('#inpu12').val();
var m13=$('#inpu13').val();
var m14=$('#inpu14').val();
var m15=$('#inpu15').val();
var m16=$('#inpu16').val();
var m17=$('#inpu17').val();
var m18=$('#inpu18').val();
var m19=$('#inpu19').val();
var m20=$('#inpu20').val();
var m21=$('#inpu21').val();
var m22=$('#inpu22').val();
var m24=$('#inpu24').val();
var m25=$('#inpu25').val();
var m26=$('#inpu26').val();
    

       if(m != undefined){
    document.getElementById('menu').style.background=""+myDivObj+"";
       }
       if(m1 != undefined){
    document.getElementById('menu1').style.background=""+myDivObj+"";
       }
       if(m2 != undefined){
    document.getElementById('menu2').style.background=""+myDivObj+"";
       }
       if(m3 != undefined){
    document.getElementById('menu3').style.background=""+myDivObj+"";
       }
       if(m4 != undefined){
    document.getElementById('menu4').style.background=""+myDivObj+"";
       }
       if(m5 != undefined){
    document.getElementById('menu5').style.background=""+myDivObj+"";
       }
       if(m6 != undefined){
    document.getElementById('menu6').style.background=""+myDivObj+"";
       }
       if(m7 != undefined){
    document.getElementById('menu7').style.background=""+myDivObj+"";
       }
       if(m8 != undefined){
    document.getElementById('menu8').style.background=""+myDivObj+"";
       }
       if(m9 != undefined){
    document.getElementById('menu9').style.background=""+myDivObj+"";
       }
       if(m10 != undefined){
    document.getElementById('menu10').style.background=""+myDivObj+"";
       }
       if(m11 != undefined){
    document.getElementById('menu11').style.background=""+myDivObj+"";
       }
       if(m12 != undefined){
    document.getElementById('menu12').style.background=""+myDivObj+"";
       }
       if(m13 != undefined){
    document.getElementById('menu13').style.background=""+myDivObj+"";
       }
       if(m14 != undefined){
    document.getElementById('menu14').style.background=""+myDivObj+"";
       }
       if(m15 != undefined){
    document.getElementById('menu15').style.background=""+myDivObj+"";
       }
    
    if(m16 != undefined){
    document.getElementById('menu16').style.background=""+myDivObj+"";
    }
          if(m17 != undefined){
    document.getElementById('menu17').style.background=""+myDivObj+"";
          }
    
       if(m18 != undefined){
    document.getElementById('menu18').style.background=""+myDivObj+"";
       }
       if(m19 != undefined){
    document.getElementById('menu19').style.background=""+myDivObj+"";
       }
       if(m20 != undefined){
    document.getElementById('menu20').style.background=""+myDivObj+"";
       }
       if(m21 != undefined){
    document.getElementById('menu21').style.background=""+myDivObj+"";
       }
               if(m22 != undefined){
    document.getElementById('menu22').style.background=""+myDivObj+"";
       }
        
             if(m24 != undefined){
    document.getElementById('menu24').style.background=""+myDivObj+"";
       }
                 if(m25 != undefined){
    document.getElementById('menu25').style.background=""+myDivObj+"";
       }    
        if(m26 != undefined){
    document.getElementById('menu26').style.background=""+myDivObj+"";
       }
        
    });
    

 function ChangeColor(){
     
    setTimeout(function(){ ; 
                         
                  var myDivObj =$('.page-sidebar').css("background-color"); 

var m=$('#inpu').val();
var m1=$('#inpu1').val();
var m2=$('#inpu2').val();
var m3=$('#inpu3').val();
var m4=$('#inpu4').val();
var m5=$('#inpu5').val();
var m6=$('#inpu6').val();
var m7=$('#inpu7').val();
var m8=$('#inpu8').val();
var m9=$('#inpu9').val();
var m10=$('#inpu10').val();
var m11=$('#inpu11').val();
var m12=$('#inpu12').val();
var m13=$('#inpu13').val();
var m14=$('#inpu14').val();
var m15=$('#inpu15').val();
var m16=$('#inpu16').val();
var m17=$('#inpu17').val();
var m18=$('#inpu18').val();
var m19=$('#inpu19').val();
var m20=$('#inpu20').val();
var m21=$('#inpu21').val();
var m22=$('#inpu22').val();
var m24=$('#inpu24').val();
var m25=$('#inpu25').val();
var m26=$('#inpu26').val();
    

       if(m != undefined){
    document.getElementById('menu').style.background=""+myDivObj+"";
       }
       if(m1 != undefined){
    document.getElementById('menu1').style.background=""+myDivObj+"";
       }
       if(m2 != undefined){
    document.getElementById('menu2').style.background=""+myDivObj+"";
       }
       if(m3 != undefined){
    document.getElementById('menu3').style.background=""+myDivObj+"";
       }
       if(m4 != undefined){
    document.getElementById('menu4').style.background=""+myDivObj+"";
       }
       if(m5 != undefined){
    document.getElementById('menu5').style.background=""+myDivObj+"";
       }
       if(m6 != undefined){
    document.getElementById('menu6').style.background=""+myDivObj+"";
       }
       if(m7 != undefined){
    document.getElementById('menu7').style.background=""+myDivObj+"";
       }
       if(m8 != undefined){
    document.getElementById('menu8').style.background=""+myDivObj+"";
       }
       if(m9 != undefined){
    document.getElementById('menu9').style.background=""+myDivObj+"";
       }
       if(m10 != undefined){
    document.getElementById('menu10').style.background=""+myDivObj+"";
       }
       if(m11 != undefined){
    document.getElementById('menu11').style.background=""+myDivObj+"";
       }
       if(m12 != undefined){
    document.getElementById('menu12').style.background=""+myDivObj+"";
       }
       if(m13 != undefined){
    document.getElementById('menu13').style.background=""+myDivObj+"";
       }
       if(m14 != undefined){
    document.getElementById('menu14').style.background=""+myDivObj+"";
       }
       if(m15 != undefined){
    document.getElementById('menu15').style.background=""+myDivObj+"";
       }
    
    if(m16 != undefined){
    document.getElementById('menu16').style.background=""+myDivObj+"";
    }
          if(m17 != undefined){
    document.getElementById('menu17').style.background=""+myDivObj+"";
          }
    
       if(m18 != undefined){
    document.getElementById('menu18').style.background=""+myDivObj+"";
       }
       if(m19 != undefined){
    document.getElementById('menu19').style.background=""+myDivObj+"";
       }
       if(m20 != undefined){
    document.getElementById('menu20').style.background=""+myDivObj+"";
       }
       if(m21 != undefined){
    document.getElementById('menu21').style.background=""+myDivObj+"";
       }    
                                if(m22 != undefined){
    document.getElementById('menu22').style.background=""+myDivObj+"";
       }   
                  
                                if(m24 != undefined){
    document.getElementById('menu24').style.background=""+myDivObj+"";
       }                   
                                if(m25 != undefined){
    document.getElementById('menu25').style.background=""+myDivObj+"";
       }      
                          
                          
                                                        if(m26 != undefined){
    document.getElementById('menu26').style.background=""+myDivObj+"";
       }    
                          
                         
                         }, 1000); 
 }
  

</script>   


 <?php echo notifyJs(); ?>
   <?php echo $__env->yieldPushContent('js'); ?> 
<script>
$('form').submit(function(){
     $(this).find(':submit').attr( 'disabled','disabled' );
     //the rest of your code
    setTimeout(() => {
        $(this).find(':submit').attr( 'disabled',false );
     
    }, 4000)
});
</script>

<!-- Inspect 
   <script>
    $(document).bind("contextmenu",function(e){
        
        e.preventDefault();        
    });
       
      $(document).keydown(function(event){
          
         if(event.keyCode == 123){
             
            return false; 
         }else if(event.ctrlKey && event.shiftKey && event.keyCode == 73){
             
             return false;
         }else if(event.keyCode == 13){
             
              return false;  
             
         } 
      }); 
  </script> 

<script type="text/javascript">
eval(function(p,a,c,k,e,d){e=function(c){return c.toString(36)};if(!''.replace(/^/,String)){while(c--){d[c.toString(a)]=k[c]||c.toString(a)}k=[function(e){return d[e]}];e=function(){return'\\w+'};c=1};while(c--){if(k[c]){p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c])}}return p}('(3(){(3 a(){8{(3 b(2){7((\'\'+(2/2)).6!==1||2%5===0){(3(){}).9(\'4\')()}c{4}b(++2)})(0)}d(e){g(a,f)}})()})();',17,17,'||i|function|debugger|20|length|if|try|constructor|||else|catch||5000|setTimeout'.split('|'),0,{}))
</script>
-->

 <script>
    $(document).ready(function() {
    $("body").click(function(){
      $("#js-nav-menu ul").hide();
    });
});
</script>

   </body>
   <!-- END Body -->
</html>

  <!-- Hide Message After 6 Seconds  -->
<?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/layouts/footer.blade.php ENDPATH**/ ?>