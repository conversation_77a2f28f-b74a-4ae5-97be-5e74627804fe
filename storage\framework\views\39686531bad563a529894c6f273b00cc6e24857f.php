<?php $__env->startSection('content'); ?>
   <?php
use App\Models\SalesDefaultData;
$Def=SalesDefaultData::orderBy('id','desc')->first();
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();

?>
  <title><?php echo e(trans('admin.Permission_to_exchange_goods')); ?></title>

    <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Permission_to_exchange_goods')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
        
    <form id="form" action="<?php echo e(url('AddExchangeGoods')); ?>" method="post" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>

               <?php echo view('honeypot::honeypotFormFields'); ?>
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i>   <?php echo e(trans('admin.Permission_to_exchange_goods')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                      <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span> 
                                    <div class="panel-content">
                                    <div class="form-row">
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Code')); ?> </label>
                                            <input type="text" value="<?php echo e($Code); ?>" class="form-control " disabled>
                                            <input type="hidden" name="Code" value="<?php echo e($Code); ?>" class="form-control">
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                           <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                                            <select class="select2 form-control w-100" id="store" name="Store" required>
                                                <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($stor->id); ?>" <?php if($Def->Store == $stor->id): ?> selected <?php endif; ?>>
                                          
                                         <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>            
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                            </select>
                                        </div>

                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($coin->id); ?>" <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?>>
                                           
                                      <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>                 
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                            <input type="text" name="Draw" value="1" class="form-control" required>
                                        </div>
   
      
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Center')); ?> </label>
                                            <select class="select2 form-control w-100" name="Cost_Center">
                                            <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>
                                            <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($cost->id); ?>">
                                               <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?>  
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>
               
                                        <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Account')); ?></label>
                       <select  class="select2 form-control w-100"  name="Account" required>
                <option value=""> <?php echo e(trans('admin.Account')); ?></option>
                                            <?php $__currentLoopData = $Accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $acc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($acc->id); ?>">
                                              <?php echo e(app()->getLocale() == 'ar' ?$acc->Name :$acc->NameEn); ?>  
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>       
                                        </div>

                                   
                                        
                                        <div class="form-group col-lg-4">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Notes')); ?>  </label>
                                            <input type="text" name="Note" value="<?php echo e(old('Note')); ?>" class="form-control">
                                        </div>
                                    </div>         
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="col-xl-12">
                        <div id="panel-1" class="panel">
                            <div class="form-group col-lg-12">
                                <label class="form-label" for="simpleinput">  </label>
                                <div class="input-items" style="position:relative;">
                 <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                                      <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                                </div>
                             </div>
                            <div class="panel-container show">
                                <div class="panel-content">
                                      <div id="mobile-overflow">
                                    <table 
                                    class="table table-bordered table-hover table-striped w-100 mobile-width-more table-color1">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(trans('admin.Name')); ?></th>
                                            <th><?php echo e(trans('admin.Unit')); ?></th>
                                            <th><?php echo e(trans('admin.Code')); ?></th>
                                            <th><?php echo e(trans('admin.AvQty')); ?></th>
                                            <th><?php echo e(trans('admin.Qty')); ?></th>
                                      
                                            <th><?php echo e(trans('admin.Store')); ?></th>
                                                <?php if($show->Expire_Date == 1): ?>
                                            <th><?php echo e(trans('admin.Exp_Date')); ?></th>
                                            <?php endif; ?>
                                            <th><?php echo e(trans('admin.Actions')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody class="Data">
                                       
                                    </tbody>
                                    </table>
                                    </div>
                                    <!-- datatable start -->
                                      <div id="mobile-overflow">
                                    <table id="dt"
                                        class="table table-bordered table-hover table-striped w-100 mobile-width-more table-color2">
                                        <thead>
                                            <tr>
                                            <th><?php echo e(trans('admin.Name')); ?></th>
                                            <th><?php echo e(trans('admin.Unit')); ?></th>
                                            <th><?php echo e(trans('admin.Code')); ?></th>
                                            <th><?php echo e(trans('admin.AvQty')); ?></th>    
                                            <th><?php echo e(trans('admin.Qty')); ?></th>                         
                                         
                                            <th><?php echo e(trans('admin.Store')); ?></th>
                                                    <?php if($show->Expire_Date == 1): ?>
                                            <th><?php echo e(trans('admin.Exp_Date')); ?></th>
                                                <?php endif; ?>
                                            <th><?php echo e(trans('admin.Actions')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody id="data-dt">
                                         
                                           
                                        </tbody>
                                    </table>
                                    </div>
                                    <!-- datatable end -->
                                    <div class="form-row">
                                        <div class="form-group col-lg-4">
                                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Numbers')); ?> </label>
                      <input type="text" id="Product_Numbers" disabled  class="form-control">
                      <input type="hidden" id="Product_NumbersHide" name="Product_Numbers">
                                        </div>
                                        <div class="form-group col-lg-4">
                         <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Qty')); ?> </label>
                                           <input type="text" id="Total_Qty" disabled  class="form-control">
                                            <input type="hidden" id="Total_QtyHide" name="Total_Qty">
                                               <input type="hidden" id="Total_DiscountHide" name="Total_Discount">
                                         <input type="hidden" id="Total_Bf_TaxesHide" name="Total_BF_Taxes">
                                              <input type="hidden" id="Total_TaxesHide" name="Total_Taxes">
                                           <input type="hidden" id="Total_PriceHide" name="Total_Price">
                                        </div>
                                        
                                                    
                                        
                                       
                                    </div>
                                    <div class="buttons mt-3" id="Submit" style="display: none">
                          <input type="hidden" id="sp" name="SP">                 
            <button type="button"  class="btn btn-primary" onclick="SPS()"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>
                                        
          <button type="button"  class="btn btn-primary" onclick="SPP()"><i class="fal fa-save"></i>  <?php echo e(trans('admin.SaveandPrint')); ?> </button>
                                      </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </form> 
        
                                <?php if(auth()->guard('admin')->user()->emp == 0): ?> 
     <input type="hidden" id="PUR" value="1">     
          <?php else: ?>    
  <?php if(auth()->guard('admin')->user()->cost_price_purch == 1): ?>
        
    <input type="hidden" id="PUR" value="1"> 
<?php else: ?>        
      <input type="hidden" id="PUR" value="0">    
        
 <?php endif; ?>
                                    <?php endif; ?>      
        
        
                </main>

<?php if(app()->getLocale() == 'ar' ): ?> 
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>

    <input type="hidden" id="EXPIRE" value="<?php echo e($show->Expire_Date); ?>">
    
           


<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>

    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>

<!-- Search Selecet -->
<script>
    $(document).ready(function () {
        $(function () {
            $(".select2").select2();

            $(".select2-placeholder-multiple").select2({
                placeholder: "Select State",
            });
            $(".js-hide-search").select2({
                minimumResultsForSearch: 1 / 0,
            });
            $(".js-max-length").select2({
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items",
            });
            $(".select2-placeholder").select2({
                placeholder: "Select a state",
                allowClear: true,
            });

            $(".js-select2-icons").select2({
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function (elm) {
                    return elm;
                },
            });

            function icon(elm) {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text;
            }

    
            
        });
    });
</script>

<!-- Unit Code and Name V-->
<script>
   function  UnitCodePurch(r){
   
   var countryId = $('#UnitPurch'+r).val();
   var Pro = $('#Product'+r).val();
   var client = $('#client').val();
   var code = $('#CodePurch'+r).val();
       var Store = $('#StorePurch'+r).val();  
                     if(countryId) {
                         $.ajax({
                             url: 'UnitSalesFilter/'+countryId+'/'+Pro+'/'+client+'/'+code+'/'+Store,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                                 $.each(data, function(key, value){
                                     
                     
                       $('#UnitPurchName'+r).val(data.name); 
                       $('#TaxRate'+r).val(data.rate); 
                       $('#TaxType'+r).val(data.type); 
                       $('#Price'+r).val(data.price); 
                       $('#PurchTax'+r).val(data.tax); 
                       $('#AvQty'+r).val(parseFloat(data.qty).toFixed(2)); 
                            
         
                       $('#UnitPriceOne'+r).val(data.priceOne); 
                       $('#UnitPriceTwo'+r).val(data.priceTwo); 
                       $('#UnitPriceThree'+r).val(data.priceThree); 
                              

       
   
                                 });
                                 
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       

       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();
        var AvQty = $("#AvQty"+r).val();
        var x=  <?php echo $Def->Mainus; ?> ;
 
       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
                 // x de elly hya lw byshtghl bsalb 
              if(x == 0){
    
       if(AvQty != 0){
           
           
       if(parseFloat(AvQty) >= parseFloat(Qty)){
            
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
  
        
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {
   
                         $('select[name="state"]').empty();
                     }
   
   }   
</script>  

<!-- Unit Code and Name -->
<script>
   function  UnitCodePurchh(r){
   
   var countryId = $('#UnitPurch'+r).val();
   var Pro = $('#Product'+r).val();
   var client = $('#client').val();
   var code = $('#CodePurch'+r).val();
      var Store = $('#StorePurch'+r).val();   
                     if(countryId) {
                         $.ajax({
                             url: 'UnitSalesFilter/'+countryId+'/'+Pro+'/'+client+'/'+code+'/'+Store,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                                 $.each(data, function(key, value){
                                     
                       $('#CodePurch'+r).val(data.code); 
                       $('#UnitPurchName'+r).val(data.name); 
                       $('#TaxRate'+r).val(data.rate); 
                       $('#TaxType'+r).val(data.type); 
                       $('#Price'+r).val(data.price); 
                       $('#PurchTax'+r).val(data.tax); 
                    $('#AvQty'+r).val(parseFloat(data.qty).toFixed(2)); 
                                     
                       $('#UnitPriceOne'+r).val(data.priceOne); 
                       $('#UnitPriceTwo'+r).val(data.priceTwo); 
                       $('#UnitPriceThree'+r).val(data.priceThree); 
                              

       
   
                                 });
                                 
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       

       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();
        var AvQty = $("#AvQty"+r).val();
       var x=  <?php echo $Def->Mainus; ?> ;
 
       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
                 // x de elly hya lw byshtghl bsalb 
              if(x == 0){
    
       if(AvQty != 0){
           
           
       if(parseFloat(AvQty) >= parseFloat(Qty)){
            
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
  
        
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {
   
                         $('select[name="state"]').empty();
                     }
   
   }   
</script>  

<!-- Store Name -->
<script>
   function  StoreNamePurch(r){
   
   var countryId = $('#StorePurch'+r).val();
   var Product = $('#Product'+r).val();
   var Code = $('#CodePurch'+r).val();
     var Unit = $('#UnitPurch'+r).val();   
                     if(countryId) {
                         $.ajax({
                             url: 'StoreNameSalesFilter/'+countryId+'/'+Product+'/'+Code+'/'+Unit,
                             type:"GET",
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                                 $.each(data, function(key, value){
                                     
                       $('#StorePurchName'+r).val(data.name); 
                       $('#AvQty'+r).val(data.AvQty); 

                                     
   
                                 });
                                 
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       

       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();
  var AvQty = $("#AvQty"+r).val();
  var x=  <?php echo $Def->Mainus; ?> ;
 
                                 
                                 
                                 
       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != '' && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
          // x de elly hya lw byshtghl bsalb 
      if(x == 0){
    
       if(AvQty != 0){
           
           
       if(parseFloat(AvQty) >= parseFloat(Qty)){
            
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
  
     
        
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                     } else {
   
                         $('select[name="state"]').empty();
                     }
   
   }   
</script>  

<!--  Filter Products -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '',store='')
    {  
     $.ajax({
      url:'ExchangeProductsFilter',
      method:'GET',
      data:{search:search,store:store},
      dataType:'json',
      success:function(data)
      {
       $('.Data').html(data.table_data);
      }
     })
    }
    
   $(document).on('keyup', '#search', function(){
     var search = $(this).val();     
     var store = $('#store').val();     
     fetch_customer_data(search,store);
    });
       
       
    $(document).on('change', '#store', function(){
     var store = $(this).val();     
     var search = $('#search').val();       
     fetch_customer_data(search,store);
    });
                 
   });
</script>

<!-- Add Products -->
<script>
   function Fun(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitPurch"+r).val();
             var UnitName = $("#UnitPurchName"+r).val();
             var Qty = $("#Qty"+r).val();
             var AvQty = $("#AvQty"+r).val();
             var Barcode = $("#CodePurch"+r).val();
             var Price = $("#Price"+r).val();
             var Total = $("#Total"+r).val();
             var ExpDate = $("#ExpDate"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var TotalTax = $("#Tax"+r).val();
             var Discount = $("#Discount"+r).val();
             var TotalBFTax = $("#TotalBFTax"+r).val();
             var StorePurch = $("#StorePurch"+r).val();
             var StorePurchName = $("#StorePurchName"+r).val();
             var PurchTax = $("#PurchTax"+r).val();
             var VOne = $("#VOne"+r).val();
             var VTwo = $("#VTwo"+r).val();
             var V_Name = $("#V_Name"+r).val();
             var VV_Name = $("#VV_Name"+r).val();
             var Viro = '';
             var VViro = '';
              var PUR=$("#PUR").val();
       if( V_Name != ''){
           
          Viro= '(' + V_Name +')';
       }
  
       if( VV_Name != ''){
           
         VViro= '(' + VV_Name +')'; 
       }

               var LANG = $("#LANG").val();
   if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          } 
             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";
   

            var EXPIRE = $("#EXPIRE").val();
    if(parseFloat(EXPIRE) == 1 ){ 
          var Exp =  "<td><input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>" + ExpDate + "</td>";
          }else{
             var Exp = "<input type='hidden' name='Exp_Date[]' value='"+ExpDate+"'>";   
          } 
           
           
        var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + ""+ Viro +" "+ VViro +"</td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='P_Code[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='AvQty[]' value='"+AvQty+"'>" + AvQty + "</td><td><input class='Qun' type='hidden' name='Qty[]' value='"+Qty+"'>" + Qty + "</td><td><input  type='hidden' name='StorePurch[]' value='"+StorePurch+"'>" + StorePurchName + "</td>"+Exp+"<td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value='"+VOne+"'><input type='hidden' name='VTwo[]' value='"+VTwo+"'></td><input type='hidden' name='V_Name[]' value='"+Viro+"'><input type='hidden' name='VV_Name[]' value='"+VViro+"'><input type='hidden' name='PurchTax[]' value='"+PurchTax+"'><input type='hidden' name='Price[]' value='"+Price+"'><input class='Disc' type='hidden' name='Discount[]' value='"+Discount+"'><input class='TotalBFTax' type='hidden' name='TotalBFTax[]' value='"+TotalBFTax+"'><input class='TotalTax' type='hidden' name='TotalTax[]' value='"+TotalTax+"'><input class='Tot' type='hidden' name='Total[]' value='"+Total+"'></tr>";
             

       
       
            
             $("#data-dt").append(markup);
    
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
   var rowctr = $('#dt').rowCount();     
                
    var sumQ = 0;        
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });   
            
    var sumT = 0;        
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });       
       
        var sumD = 0;        
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   }); 
       
       
           var sumBF = 0;        
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   }); 
       
           var sumTax = 0;        
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   }); 
       
   
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));
       
   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));
       
   $('#Total_Price').val(parseFloat(sumT));
   $('#Total_PriceHide').val(parseFloat(sumT));
     
    
   $('#Total_Discount').val(parseFloat(sumD));
   $('#Total_DiscountHide').val(parseFloat(sumD));
       
   $('#Total_Bf_Taxes').val(parseFloat(sumBF));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF));
       
   $('#Total_Taxes').val(parseFloat(sumTax));
   $('#Total_TaxesHide').val(parseFloat(sumTax));
       

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
       
            var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        
      if(Pay == 'Later' &&  Stat == 0 ){
               
             if(paid == 0 || paid < 0){   
        document.getElementById("Submit").style.display = "none";  
             }else{
                 
           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
                 
                 
                 
             }
           
       }else{
           
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }   
       }
 
        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
            
   
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
   var rowctr = $('#dt').rowCount();     
                
    var sumQ = 0;        
   $('.Qun').each(function(){
   sumQ += parseFloat($(this).val());
   });   
            
    var sumT = 0;        
   $('.Tot').each(function(){
   sumT += parseFloat($(this).val());
   });       
       
        var sumD = 0;        
   $('.Disc').each(function(){
   sumD += parseFloat($(this).val());
   }); 
       
       
           var sumBF = 0;        
   $('.TotalBFTax').each(function(){
   sumBF += parseFloat($(this).val());
   }); 
       
           var sumTax = 0;        
   $('.TotalTax').each(function(){
   sumTax += parseFloat($(this).val());
   }); 
       
   
   $('#Product_Numbers').val(parseFloat(rowctr));
   $('#Product_NumbersHide').val(parseFloat(rowctr));
       
   $('#Total_Qty').val(parseFloat(sumQ));
   $('#Total_QtyHide').val(parseFloat(sumQ));
       
   $('#Total_Price').val(parseFloat(sumT));
   $('#Total_PriceHide').val(parseFloat(sumT));
     
    
       $('#Total_Discount').val(parseFloat(sumD));
   $('#Total_DiscountHide').val(parseFloat(sumD));
       
          $('#Total_Bf_Taxes').val(parseFloat(sumBF));
   $('#Total_Bf_TaxesHide').val(parseFloat(sumBF));
       
          $('#Total_Taxes').val(parseFloat(sumTax));
   $('#Total_TaxesHide').val(parseFloat(sumTax));
       

       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
            
                  var Pay =$('#Payment_Method').val();
        var Stat =$('#Status').val();
        var paid =$('#paid').val();
        
      if(Pay == 'Later' &&  Stat == 0 ){
               
             if(paid == 0 || paid < 0){   
        document.getElementById("Submit").style.display = "none";  
             }else{
                 
           if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
                 
                 
                 
             }
           
       }else{
           
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }   
       }
 
     
      
       
       
   
                    })  
       
       
     }    
</script> 

<!-- Total Products -->
<script>
   function PurchTotal(r) { 
   var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var AvQty = $("#AvQty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       
 var x=  <?php echo $Def->Mainus; ?> ;
       
       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
      // x de elly hya lw byshtghl bsalb 
    if(x == 0){
    
       if(parseFloat(AvQty) != 0){
           
           
        if(parseFloat(AvQty) >= parseFloat(Qty)){
          
          
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
  
 
       
   }
</script>

<!-- Scanner  problem -->         
<script>
   $(".form-control").keypress(function(event){
   if (event.which == '10' || event.which == '13') {
   event.preventDefault();
   }
   });             
   
   
</script>

<!-- Submit Script -->
<script>

        function SPP(){
           $('#sp').val(1);  
           var x= $('#sp').val();   
        
        if(x == 1){
             document.getElementById("Submit").style.display = "none"; 
      document.getElementById('form').submit();   
        }
    }
    
    
       function SPS(){
           $('#sp').val(0);  
           var x= $('#sp').val();   
        
        if(x == 0){
             document.getElementById("Submit").style.display = "none"; 
      document.getElementById('form').submit();   
        }
    }

      function SPP8(){
           $('#sp').val(2);  
           var x= $('#sp').val();   
        
        if(x == 2){
             document.getElementById("Submit").style.display = "none"; 
      document.getElementById('form').submit();   
        }
    }
    
    
 
</script>

<!-- Change Price -->
<script>
function ChangePriceU(r){
    
  var PriceOne = $("#UnitPriceOne"+r).val();
       $("#Price"+r).val(parseFloat(PriceOne).toFixed(2));
    
 var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var AvQty = $("#AvQty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       
 var x=  <?php echo $Def->Mainus; ?> ;
       
       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
      // x de elly hya lw byshtghl bsalb 
    if(x == 0){
    
       if(parseFloat(AvQty) != 0){
           
           
        if(parseFloat(AvQty) >= parseFloat(Qty)){
          
          
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
      
    
}
    
    
function ChangePriceUU(r){
    
  var PriceTwo = $("#UnitPriceTwo"+r).val();
       $("#Price"+r).val(parseFloat(PriceTwo).toFixed(2));
    
 var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var AvQty = $("#AvQty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       
 var x=  <?php echo $Def->Mainus; ?> ;
       
       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
      // x de elly hya lw byshtghl bsalb 
    if(x == 0){
    
       if(parseFloat(AvQty) != 0){
           
           
        if(parseFloat(AvQty) >= parseFloat(Qty)){
          
          
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
      
    
}
    
    
    
function ChangePriceUUU(r){
    
  var PriceThree = $("#UnitPriceThree"+r).val();
       $("#Price"+r).val(parseFloat(PriceThree).toFixed(2));
    
 var TaxRate = $("#TaxRate"+r).val();
   var TaxType = $("#TaxType"+r).val();
   var Qty = $("#Qty"+r).val();
   var AvQty = $("#AvQty"+r).val();
   var Price = $("#Price"+r).val();
   var Discount = $("#Discount"+r).val();
   var i = 0;
   var ii=0;       
   var iii=0;       
 var x=  <?php echo $Def->Mainus; ?> ;
       
       
       if(TaxType == 1){
           //Precent
          
      var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);  
       
       $("#TotalBFTax"+r).val(parseFloat(BF));
           
       i =    parseFloat(TaxRate)  / 100 ;  
           
        ii=  parseFloat(BF) * parseFloat(i) ;
              
           $("#Tax"+r).val(parseFloat(ii));

       iii =  parseFloat(BF) + parseFloat(ii) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
       }else if(TaxType == 2){
          //Number 
        var BF = ( parseFloat(Qty) *  parseFloat(Price) ) -  parseFloat(Discount);   
         $("#TotalBFTax"+r).val(parseFloat(BF));
          
            i =    parseFloat(TaxRate)   ; 
           
           $("#Tax"+r).val(parseFloat(i));

       iii =  parseFloat(BF) + parseFloat(i) ;
           
           $("#Total"+r).val(parseFloat(iii));
           
           
       }
       

        var Total = $("#Total"+r).val();
        var TotalBFTax = $("#TotalBFTax"+r).val();
        var Tax = $("#Tax"+r).val();
        var UnitID = $("#UnitPurch"+r).val();
        var StorePurch = $("#StorePurch"+r).val();

       if(TaxRate == ''  || TaxType == ''  ||  Qty == '' || Price == '' || Discount == '' || Total == '' || TotalBFTax == '' || Tax == '' || UnitID == '' || StorePurch == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }
   
     
       if(TaxRate != ''  && TaxType != ''  &&  Qty != ''  && Qty != 0 && Price != '' && Discount != '' && Total != '' && TotalBFTax != '' && Tax != '' && UnitID != '' && StorePurch != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  
            
      // x de elly hya lw byshtghl bsalb 
    if(x == 0){
    
       if(parseFloat(AvQty) != 0){
           
           
        if(parseFloat(AvQty) >= parseFloat(Qty)){
          
          
    document.getElementById("AddBtnPur"+r).style.display = "block";
            
        }else{
            
   document.getElementById("AddBtnPur"+r).style.display = "none";     
            
        }   
       

           }else{
               
      document.getElementById("AddBtnPur"+r).style.display = "none";         
           }    
        
        
    }else{
        
       document.getElementById("AddBtnPur"+r).style.display = "block";      
        
    }   
      
    
}
        
    
    
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/PermissionToExchangeGoods.blade.php ENDPATH**/ ?>