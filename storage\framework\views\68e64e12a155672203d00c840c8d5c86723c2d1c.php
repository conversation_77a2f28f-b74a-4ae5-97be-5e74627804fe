<?php $__env->startSection('content'); ?>
<?php
use App\Models\StoresDefaultData;
$Def=StoresDefaultData::orderBy('id','desc')->first();
use App\Models\DefaultDataShowHide;
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Stores_Transfers')); ?></title>

   <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Stores_Transfers')); ?>   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
    <form id="form" action="<?php echo e(url('AddStoresTransfers')); ?>" method="post">
        <?php echo csrf_field(); ?>

         <?php echo view('honeypot::honeypotFormFields'); ?>
        
       <?php if(auth()->guard('admin')->user()->emp != 0): ?> 
        
           <?php if(auth()->guard('admin')->user()->cost_price  == 1): ?> 
            <input type="hidden" id="COSTPRICE" value="1">
        <?php else: ?>
            <input type="hidden" id="COSTPRICE" value="0">
        <?php endif; ?>
        
    <?php else: ?>
          <input type="hidden" id="COSTPRICE" value="1">
        <?php endif; ?>

        
        <?php if($show->Change_Way_Stores_Transfer == 1): ?>
           <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-container show">
                                    <div class="panel-content">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('التحكم في طريقه تحويل المخازن')): ?>  
                                    <div class="form-row">
                                        
                                    
                  <div class="form-group col-lg-6">
                        <label class="form-label" for=""><?php echo e(trans('admin.Type_Transfer')); ?></label>
             <select class="select2 form-control w-100" id="TypeTransfer"  name="TypeTransfer" onchange="ChangeWay()">
                                                <option value=""> <?php echo e(trans('admin.Type_Transfer')); ?></option>
                                                <option value="1"> <?php echo e(trans('admin.Sale_Price')); ?></option>
                                                <option value="0"> <?php echo e(trans('admin.Cost_Price')); ?></option>
                                            </select>
                                        </div> 
                                        
                                        
                         <div class="form-group col-lg-6" id="OtherType" style="display: none">
                        <label class="form-label" for=""><?php echo e(trans('admin.Cost_Store')); ?></label>
             <select class="select2 form-control w-100" id="Cost_Store"  name="Cost_Store">
                                                <option value=""> <?php echo e(trans('admin.Cost_Store')); ?></option>
                                                <option value="1"> <?php echo e(trans('admin.From_Store')); ?></option>
                                                <option value="2"> <?php echo e(trans('admin.To_Store')); ?></option>
                                            </select>
                                        </div>                     
                                        
                                        
                                        </div>
                                <?php endif; ?>        
                                        
                                        
                                    </div>
                                </div>
                            </div>
               </div>
        </div>
        <?php endif; ?> 
                                        
      
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i> <?php echo e(trans('admin.Stores_Transfers')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                  <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>       
                                    <div class="panel-content">
                                <form action="">
                                    <div class="form-row">
                                        <div class="form-group col-lg-3">
                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?>  </label>
                             <input type="text"  value="<?php echo e($Code); ?>" class="form-control " disabled>
                             <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                        </div>
                                      <?php if(auth()->guard('admin')->user()->emp == 0): ?>
                      <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                        
                                         <?php if(auth()->guard('admin')->user()->Date == 1): ?>
                                   <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                </div>
                                        <?php else: ?>
                                 <div class="form-group col-lg-2">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Date')); ?></label>
                                    <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required readonly>
                                </div>
                                        <?php endif; ?>
                                        
                                        <?php endif; ?>
                                      
                                        
                                        <div class="form-group col-lg-3">
                              <span id="Alert" style="display: none; color: red; position: absolute';margin-right: 130px;">   
                                        <?php echo e(trans('admin.AlertStoresClientAccount')); ?>

                                        </span>                     
                        <label class="form-label" for=""><?php echo e(trans('admin.From_Store')); ?></label>
             <select class="select2 form-control w-100" id="store" name="From_Store" required>
                                                <option value=""> <?php echo e(trans('admin.From_Store')); ?></option>
                                            <?php $__currentLoopData = $StoresFrom; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($store->id); ?>">
                  <?php echo e(app()->getLocale() == 'ar' ?$store->Name :$store->NameEn); ?>

                 
                 </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                            </select>
                                        </div>
                                           <div class="form-group col-lg-3">
                        <label class="form-label" for=""><?php echo e(trans('admin.To_Store')); ?></label>
             <select class="select2 form-control TOSTORE w-100" id="To"  name="To_Store" required>
                                                <option value=""> <?php echo e(trans('admin.To_Store')); ?></option>
                                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($store->id); ?>">
                   <?php echo e(app()->getLocale() == 'ar' ?$store->Name :$store->NameEn); ?>

                 </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                            </select>
                                        </div>      
                                        
                                        <?php if($Def->Show_Ship == 1): ?>
                                                <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Shipping_Compaines')); ?></label>
                                            <select class="select2 form-control w-100"  name="Ship" >
                                                <option value=""> <?php echo e(trans('admin.Shipping_Compaines')); ?></option>
                                            <?php $__currentLoopData = $Shippings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($ship->id); ?>">
                               
                                      <?php echo e(app()->getLocale() == 'ar' ?$ship->Name :$ship->NameEn); ?>                
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                            </select>
                                        </div>
                                                    <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.CostShip')); ?></label>
                                           <input class="form-control" type="number" step="any" name="CostShip" value="0">
                                        </div>
                                        <?php endif; ?>
                                        
                                        
                                        
                                    <?php if($show->Coin == 1): ?>    
                             <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
      <option value="<?php echo e($coin->id); ?>" <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?>>
                                     <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->Arabic_Name); ?>             
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                                            </select>
                                        </div>
                                    <?php else: ?>
                                       <div class="form-group col-lg-4" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
      <option value="<?php echo e($coin->id); ?>" <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?>>
                                  <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>                    
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                                            </select>
                                        </div>   
                                    <?php endif; ?>
                                        
                                                <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                                            <select class="select2 form-control w-100" name="Delegate">
                                        <option value=""><?php echo e(trans('admin.Delegate')); ?></option>        
                                            <?php $__currentLoopData = $Emps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($emp->id); ?>">
                      
                                                   <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>           
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div> 
                                        
                                                                <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Branch')); ?></label>
                                            <select class="select2 form-control w-100" name="Branch">
                                        <option value=""><?php echo e(trans('admin.Branch')); ?></option>        
                                            <?php $__currentLoopData = $Branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bran): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($bran->id); ?>">
                                          
                                                    
                                                      <?php echo e(app()->getLocale() == 'ar' ?$bran->Arabic_Name :$bran->English_Name); ?>              
                                                </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div> 
                                        
                                        
                                        
                                               <?php if($show->Draw == 1): ?>
                                        <div class="form-group col-lg-4">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                        <input type="number" step="any" value="1" name="Draw" class="form-control" required>
                                        </div>     
                                        <?php else: ?>
                                         <div class="form-group col-lg-4" style="display: none">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                        <input type="number" step="any" value="1" name="Draw" class="form-control" required>
                                        </div>    
                                        <?php endif; ?>
                                        <div class="form-group col-lg-8">
                                            <div class="input-items" style="position:relative;">
                   <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                         
                                      <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                                            </div>
                                         </div>
                                          <?php if($show->Group_Brand == 1): ?>
                                      <div class="form-group col-lg-2">
                                       <select class="select2 form-control w-100" id="Brandd">
                                          <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                          <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">
                                             <?php echo e(app()->getLocale() == 'ar' ?$brand->Name :$brand->NameEn); ?>           
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                            </div>
                            <div class="form-group col-lg-2">
                                       <select class="select2 form-control w-100" id="Groupp">
                                          <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                          <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                 <option value="<?php echo e($group->id); ?>">
                                     <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>           
                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                            </div>
                                        <?php endif; ?>
                                        
                                        
              
                                         <?php if($show->Show_File_TransferStores == 1): ?>
                                     <div class="form-group col-lg-12">
                                    <label class="form-label" for="simpleinput"><?php echo e(trans('admin.File')); ?></label>
                                    <input type="file" name="File"  class="form-control">
                                </div>    
                                <?php endif; ?>                     
                                        
                                        
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="col-xl-12">
                        <div id="panel-1" class="panel">
                          
                            <div class="panel-container show">
                                <div class="panel-content">
                                 
                                    <!-- datatable start -->    
                                    <div id="mobile-overflow">
                    <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100 mobile-width th-width table-color1">
                                        <thead>
                                            <tr>
                                                <th> <?php echo e(trans('admin.Name')); ?></th>
                                                <th> <?php echo e(trans('admin.Unit')); ?></th>
                                                <th> <?php echo e(trans('admin.Code')); ?></th>
                                                
                                            <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                                                
                                                <?php if(auth()->guard('admin')->user()->cost_price  == 1): ?>
                                                      <th> <?php echo e(trans('admin.Price')); ?></th>
                                                <?php endif; ?>
                                                
                                            <?php else: ?>    
                                                <th> <?php echo e(trans('admin.Price')); ?></th>
                                            <?php endif; ?>    
                                                <th> <?php echo e(trans('admin.Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Trans_Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Actions')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody class="Data">

                                        </tbody>
                                    </table>
                                    </div>
                                    <!-- datatable end -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div id="mobile-overflow">
                              <table id="dt" class="table table-bordered table-hover table-striped w-100 mobile-width table-color2">
                                            <thead>
                                                <tr>
                                                <th> <?php echo e(trans('admin.Name')); ?></th>
                                                <th> <?php echo e(trans('admin.Unit')); ?></th>     
                                                <th> <?php echo e(trans('admin.Code')); ?></th>
                                                 <?php if(auth()->guard('admin')->user()->emp != 0): ?>
                                                
                                                <?php if(auth()->guard('admin')->user()->cost_price  == 1): ?>
                                                      <th> <?php echo e(trans('admin.Price')); ?></th>
                                                <?php endif; ?>
                                                
                                            <?php else: ?>    
                                                <th> <?php echo e(trans('admin.Price')); ?></th>
                                            <?php endif; ?>   
                                                <th> <?php echo e(trans('admin.Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Trans_Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody id="data-dt">
                                                
                                            </tbody>
                                        </table>
                                        </div>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-lg-6">
                                  <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Total_Price_Trans_Qty')); ?></label>
                                            <input type="text" id="Total" disabled  class="form-control">
                                    <input type="hidden" id="TotalHide" name="Total"  class="form-control">
                                    <input type="hidden" id="Total_Cost" name="Total_Cost"  class="form-control">
                                        </div>  
                                      <div class="form-group col-lg-6">
                                  <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Total_Trans_Qty')); ?></label>
                                            <input type="text" id="TotalQty" disabled  class="form-control">
                                    <input type="hidden" id="TotaQtylHide" name="TotalQty"  class="form-control">
                                        </div>              
                                        </div>
                                    
                                   <div class="row">
                                    <div class="form-group col-md-12">
                                        <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Notes')); ?>  </label>

                            <textarea class="js-summernote" id="saveToLocal" name="Note">
                                        <?php echo e(old('Note')); ?>

                                        </textarea>            
                                    </div>
                                   </div>
                <div class="buttons mt-3" id="Submit" style="display: none">

                     <input type="hidden" id="sp" name="SP">                 
            <button type="button"  class="btn btn-primary" onclick="SPS()"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>
                      
        <button type="button"  class="btn btn-primary" onclick="SPP()"><i class="fal fa-save"></i>  <?php echo e(trans('admin.SaveandPrint')); ?> </button>                        
                                        
                                        
                                      </div>
                                </div>
                            </div>
                        </div>
                    </div>

<input type="hidden" id="Open" value="<?php echo e($Def->StoresTarnsferPrice); ?>">

                    </div>
        </form> 
                </main>

<?php if(app()->getLocale() == 'ar' ): ?> 
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
  <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
      <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>

<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }
                    
             
            });
        });



    </script>

<!--  Filter Product -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
       
       
      
    function fetch_customer_data(search = '',store = '',Brand='',Group='',TypeTransfer='',Cost_Store='',TOSTORE='')
    {
     $.ajax({
      url:'TarnsferStoresFilter',
      method:'GET',
      data:{
          search:search,
          store:store,
          Brand:Brand,
          Group:Group,
          TypeTransfer:TypeTransfer,
          Cost_Store:Cost_Store,
          TOSTORE:TOSTORE
      },
      dataType:'json',
      success:function(data)
      {
       $('.Data').html(data.table_data);
      }
     })
    }
    
      $(document).on('keyup', '#search', function(){
     var search = $(this).val();     
     var store = $('#store').val();     
     var Tostore = $('#To').val();     
     var Brand = $('#Brandd').val();     
     var Group = $('#Groupp').val();     
     var TypeTransfer = $('#TypeTransfer').val();     
     var Cost_Store = $('#Cost_Store').val();     
       var TOSTORE=$('.TOSTORE').val();
  var FS = $('#store').val();     
     var TS = $('#To').val();   
      if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
     fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
                  
       fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }
    });
       
      $(document).on('change', '#store', function(){
     var store = $(this).val();     
     var search = $('#search').val();     
         var Tostore = $('#To').val();     
           var Brand = $('#Brandd').val();     
     var Group = $('#Groupp').val();  
              var TypeTransfer = $('#TypeTransfer').val();     
     var Cost_Store = $('#Cost_Store').val();
 var TOSTORE=$('.TOSTORE').val();
  var FS = $('#store').val();     
     var TS = $('#To').val();   
          if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
  fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
   fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }      
          
          
    });   

       $(document).on('change', '#Brandd', function(){
     var Brand = $(this).val();     
     var store = $('#store').val();     
     var Tostore = $('#To').val();     
     var search = $('#search').val();     
     var Group = $('#Groupp').val(); 
                    var TypeTransfer = $('#TypeTransfer').val();     
     var Cost_Store = $('#Cost_Store').val();
            var TOSTORE=$('.TOSTORE').val();
   var FS = $('#store').val();     
     var TS = $('#To').val();   
         if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
    fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
                  
   fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }
    });
       
       $(document).on('change', '#Groupp', function(){
     var Group = $(this).val();     
     var store = $('#store').val();     
     var Tostore = $('#To').val();     
     var Brand = $('#Brandd').val();     
     var search = $('#search').val();     
         var TypeTransfer = $('#TypeTransfer').val();     
     var Cost_Store = $('#Cost_Store').val();
            var TOSTORE=$('.TOSTORE').val();
    var FS = $('#store').val();     
     var TS = $('#To').val();      
       if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
  fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
 fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }
    });

       $(document).on('change', '#To', function(){
           
           
     var Tostore = $(this).val();     
     var search = $('#search').val();     
         var store = $('#store').val();     
           var Brand = $('#Brandd').val();     
     var Group = $('#Groupp').val();  
                        var TypeTransfer = $('#TypeTransfer').val();     
     var Cost_Store = $('#Cost_Store').val();
            var TOSTORE=$('.TOSTORE').val();
  var FS = $('#store').val();     
     var TS = $('#To').val();   
          if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
     fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
                  
    fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }      
          
          
    });   
       
          $(document).on('change', '#TypeTransfer', function(){
     var TypeTransfer = $(this).val();     
     var search = $('#search').val();     
         var store = $('#store').val();     
           var Brand = $('#Brandd').val();     
     var Group = $('#Groupp').val();  
                        var Tostore = $('#To').val();     
     var Cost_Store = $('#Cost_Store').val();
               var TOSTORE=$('.TOSTORE').val();
  var FS = $('#store').val();     
     var TS = $('#To').val();   
          if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
    fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
                  
   fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }      
          
          
    });   
       
       
       $(document).on('change', '#Cost_Store', function(){
     var Cost_Store = $(this).val();     
     var search = $('#search').val();     
         var store = $('#store').val();     
           var Brand = $('#Brandd').val();     
     var Group = $('#Groupp').val();  
                        var TypeTransfer = $('#TypeTransfer').val();     
            var TOSTORE=$('.TOSTORE').val();
     var Tostore = $('#To').val();
  var FS = $('#store').val();     
     var TS = $('#To').val();   
          if(store != Tostore){
          
      var Open = $('#Open').val();
      if(parseFloat(Open) == 1){
        $.ajax({
                              url: 'StoreClientAccount',
                              type:"GET",
                              data:{
                              FS:FS,
                              TS:TS
                              },
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                       
                                  
                                  if(parseFloat(data.Account) != 2){
                                     document.getElementById('Submit').style.display="none";
                                     document.getElementById('Alert').style.display="block";
                                      
                                     }else{
                                     
                                      document.getElementById('Submit').style.display="block";
                                      document.getElementById('Alert').style.display="none";
                                                   
     fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
                                     }
   
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
      

    }else{
        
                  
    fetch_customer_data(search,store,Brand,Group,TypeTransfer,Cost_Store,TOSTORE);
    }      

      }else{
          
        alert('لا يمكن اختيار نفس المخزن مرتين');  
      }      
          
          
    });
       
      
 
   });
</script>

<!-- Unit Code and Name -->
<script>
    function  UnitCodeStartt(x){
    
    var countryId = $('#UnitStart'+x).val();
    var Pro = $('#Product'+x).val();
       var code = $('#CodeStart'+x).val();
      var PRORATE = $('#PRORATE'+x).val();    
    var store = $('#store').val();       
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeStoresTransferFilter/'+countryId+'/'+Pro+'/'+code+'/'+store+'/'+PRORATE,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                        $('#CodeStart'+x).val(data.code); 
                        $('#UnitStartName'+x).val(data.name); 
                        $('#Price'+x).val(data.price); 
                        $('#Qty'+x).val(data.qty); 
                                      

                                  });
                                    
       var Price =$('#Price'+x).val(); 
     var Trans_Qty =$('#Trans_Qty'+x).val(); 
var UnitID = $('#UnitStart'+x).val();
  var Qty =$('#Qty'+x).val();   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(parsFloat(result)); 

      
       if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
      if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           
        document.getElementById("AddBtnPur"+x).style.display = "block";   
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }         
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

}   
</script>    

<!-- Unit Code and Name  V-->
<script>
    function  UnitCodeStart(x){
    
    var countryId = $('#UnitStart'+x).val();
    var Pro = $('#Product'+x).val();
       var code = $('#CodeStart'+x).val();
    var store = $('#store').val();       
      var PRORATE = $('#PRORATE'+x).val();       
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeStoresTransferFilter/'+countryId+'/'+Pro+'/'+code+'/'+store+'/'+PRORATE,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                        $('#UnitStartName'+x).val(data.name); 
                        $('#Price'+x).val(data.price); 
                        $('#Qty'+x).val(data.qty); 
                                      

                                  });
                                    
       var Price =$('#Price'+x).val(); 
     var Trans_Qty =$('#Trans_Qty'+x).val(); 
var UnitID = $('#UnitStart'+x).val();
  var Qty =$('#Qty'+x).val();   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(parsFloat(result)); 

      
       if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
      if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           
        document.getElementById("AddBtnPur"+x).style.display = "block";   
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }         
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

}   
</script>    

<!-- Total-->
<script>
 function Totaal(x){
     
                                   
       var Price =$('#Price'+x).val(); 
       var Trans_Qty =$('#Trans_Qty'+x).val(); 
       var Qty =$('#Qty'+x).val(); 
       var UnitID = $('#UnitStart'+x).val();
   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(result); 

     
 
       if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
           if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           if(parseFloat(Trans_Qty) < 0 || parseFloat(Price) < 0){
           document.getElementById("AddBtnPur"+x).style.display = "none"; 
      
           }else{
                 document.getElementById("AddBtnPur"+x).style.display = "block";     
           }
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }
     }  
            
         
                
     
 }
</script>

<!-- Add Products -->
<script>
   function Fun(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitStart"+r).val();
             var UnitName = $("#UnitStartName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodeStart"+r).val();
             var Price = $("#Price"+r).val();
             var OldPrice = $("#OldPrice"+r).val();
             var Trans_Qty = $("#Trans_Qty"+r).val();
             var Total = $("#TotalPrice"+r).val();
             var V_Name = $("#V_Name"+r).val();
             var VOne = $("#VOne"+r).val();
             var VV_Name = $("#VV_Name"+r).val();
             var VTwo = $("#VTwo"+r).val();
             var CostPrice = $("#CostPrice"+r).val();
             var Viro = '';
             var VViro = '';

       if( V_Name != ''){
           
          Viro= '(' + V_Name +')';
       }
  
       if( VV_Name != ''){
           
         VViro= '(' + VV_Name +')'; 
       }

               var LANG = $("#LANG").val();
   if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          } 
       
       var COSTPRICE=$('#COSTPRICE').val();
       
             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";

         
       if(parseFloat(COSTPRICE) == 1){
           
           var CP='block';
       }else{
           
            var CP='none';   
       }
       
       
       
             var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + ""+ Viro +" "+ VViro +"</td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='P_Code[]' value='"+Barcode+"'>" + Barcode + "</td><td style='display:"+CP+"'><input type='hidden' name='Price[]' value='"+Price+"'>" + Price + "</td><td><input type='hidden' name='Qty[]' value='"+Qty+"'>" + Qty + "</td><td><input class='TransQty' type='hidden' name='Trans_Qty[]' value='"+Trans_Qty+"'><input class='Tot' type='hidden' name='TotalP[]' value='"+Total+"'>" + Trans_Qty + "</td><td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDInp"+r+"' type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value='"+VOne+"'><input type='hidden' name='VTwo[]' value='"+VTwo+"'><input type='hidden' name='V_Name[]' value='"+V_Name+"'><input type='hidden' name='VV_Name[]' value='"+VV_Name+"'><input type='hidden' name='OldPrice[]' value='"+OldPrice+"'><input type='hidden' class='COSTA' name='CostPrice[]' value='"+CostPrice * Trans_Qty+"'></td></tr>";
   


            var  Product_IDInp =$("#Product_IDInp"+r).val();
       
         if(Product != Product_IDInp){
             $("#data-dt").append(markup);
         }
       
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
  var rowctr = $('#dt').rowCount();     
                
   
    var sumTE = 0;        
$('.Tot').each(function(){
  sumTE += parseFloat($(this).val());
});   
       
    var sumTQ = 0;        
$('.TransQty').each(function(){
  sumTQ += parseFloat($(this).val());
});             
 
       
           var sumCost = 0;        
$('.COSTA').each(function(){
  sumCost += parseFloat($(this).val());
}); 
           
       
       
 $('#Total_Cost').val(parseFloat(sumCost));
 $('#Total').val(parseFloat(sumTE));
 $('#TotalHide').val(parseFloat(sumTE));
 $('#TotalQty').val(parseFloat(sumTQ));
 $('#TotaQtylHide').val(parseFloat(sumTQ));

       

      
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
       
       

        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
            
       $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
  var rowctr = $('#dt').rowCount();     
                
      var sumTE = 0;        
$('.Tot').each(function(){
  sumTE += parseFloat($(this).val());
});           
     var sumTQ = 0;        
$('.TransQty').each(function(){
  sumTQ += parseFloat($(this).val());
});   
            
           var sumCost = 0;        
$('.COSTA').each(function(){
  sumCost += parseFloat($(this).val());
}); 
           
       
       
 $('#Total_Cost').val(parseFloat(sumCost));            
 $('#Total').val(parseFloat(sumTE));
 $('#TotalHide').val(parseFloat(sumTE));
 $('#TotalQty').val(parseFloat(sumTQ));
 $('#TotaQtylHide').val(parseFloat(sumTQ));
 
      
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
       
 
            
                    })  
       
       
     }    
</script> 

<!-- Change Price -->
<script>
function ChangePriceU(r){
    
  var PriceOne = $("#UnitPriceOne"+r).val();
       $("#Price"+r).val(parseFloat(PriceOne).toFixed(2));
    
    var Price =$('#Price'+r).val(); 
       var Trans_Qty =$('#Trans_Qty'+r).val(); 
       var Qty =$('#Qty'+r).val(); 
       var UnitID = $('#UnitStart'+r).val();
   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(result); 

            if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
           if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           
        document.getElementById("AddBtnPur"+x).style.display = "block";   
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }
     }  
    

       
    
}
    
function ChangePriceUU(r){
    
  var PriceTwo = $("#UnitPriceTwo"+r).val();
       $("#Price"+r).val(parseFloat(PriceTwo).toFixed(2));
    
    var Price =$('#Price'+r).val(); 
       var Trans_Qty =$('#Trans_Qty'+r).val(); 
       var Qty =$('#Qty'+r).val(); 
       var UnitID = $('#UnitStart'+r).val();
   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(result); 

            if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
           if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           
        document.getElementById("AddBtnPur"+x).style.display = "block";   
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }
     }  
    

       
    
}
    
function ChangePriceUUU(r){
    
  var PriceThree = $("#UnitPriceThree"+r).val();
       $("#Price"+r).val(parseFloat(PriceThree).toFixed(2));
    
    var Price =$('#Price'+r).val(); 
       var Trans_Qty =$('#Trans_Qty'+r).val(); 
       var Qty =$('#Qty'+r).val(); 
       var UnitID = $('#UnitStart'+r).val();
   
 var result = parseFloat(Trans_Qty) *  parseFloat(Price) ;  
                                  
  $('#TotalPrice'+x).val(result); 

            if(UnitID == '' || Trans_Qty == ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(UnitID != ''  && Trans_Qty != ''){
         
           if(parseFloat(Trans_Qty) <= parseFloat(Qty)){
           
        document.getElementById("AddBtnPur"+x).style.display = "block";   
               
           }else{
             document.getElementById("AddBtnPur"+x).style.display = "none";      
               
           }
     }  
    

       
    
}
</script>

<!-- Submit Script -->
<script>

        function SPP(){
           $('#sp').val(1);  
           var x= $('#sp').val();   

        if(x == 1){
             document.getElementById("Submit").style.display = "none"; 
      document.getElementById('form').submit();   
        }
          
    }
    
     function SPS(){
           $('#sp').val(0);  
           var x= $('#sp').val();   

        if(x == 0){
             document.getElementById("Submit").style.display = "none"; 
      document.getElementById('form').submit();   
        }
          
    }
  
    
 
</script>


<script>
 function ChangeWay(){
   
     var TypeTransfer=$('#TypeTransfer').val();
     
     if(parseFloat(TypeTransfer) == 0){
        
         
        document.getElementById('OtherType').style.display='block'; 
         
        
        }else{
        
             document.getElementById('OtherType').style.display='none'; 
        
        }
     
     
 } 


</script>
    
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/StoresTransfers.blade.php ENDPATH**/ ?>