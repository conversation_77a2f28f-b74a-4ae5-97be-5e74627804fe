    <?php
use App\Models\CompanyData;
$Def=CompanyData::orderBy('id','desc')->first();
?>

    
       
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">

      <meta name="description" content="Analytics Dashboard">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
      <!-- Call App Mode on ios devices -->
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <!-- Remove Tap Highlight on Windows Phone IE -->
      <meta name="msapplication-tap-highlight" content="no">
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
          <!-- Place favicon.ico in the root directory -->
      <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(URL::to($Def->Icon)); ?>">
      <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(URL::to($Def->Icon)); ?>">
      <link rel="mask-icon" href="<?php echo e(URL::to($Def->Icon)); ?>" color="#5bbad5">
       
      <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Tajawal&display=swap" rel="stylesheet">
       
    <?php 
if(!function_exists('direction')){

        function direction(){

            if(session()->has('lang')){
                if(session('lang') == 'ar'){

                 return 'rtl';
                }else{
                    return 'ltr' ;
                }
            }else{
                return 'rtl' ;
            }
        }


        }
    
?>  


    
  <?php if(direction()=='ltr'): ?> 
           <!-- base css -->
      <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
      <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
      <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
      <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
       <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/reactions/reactions.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/fullcalendar/fullcalendar.bundle.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/jqvmap/jqvmap.bundle.css')); ?>">  
      
 
     
        <!-- DEMO related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-brands.css')); ?>">
        <!-- page related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
    <style>
        th{
            width:135px!important;
        }
        
        
        .header-icon:not(.btn)[data-toggle="dropdown"] + .dropdown-menu {
    position: absolute;
    border: 0px solid #ccc;
    right: unset !important;
    top: 4.0625rem !important;
    left: auto !important;
    padding: 0;
    margin: 0;
}
        
        
.dropdown-menu .dropdown-multilevel.dropdown-multilevel-left > .dropdown-menu {
    left: 100% !important;
    right: auto !important;
}        
    </style>
       
 
  <?php else: ?>
  
       
                  <!-- base css -->
      <link id="vendorsbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/vendors.bundle.css')); ?>">
      <link id="appbundle" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/app.bundle.css')); ?>">
      <link id="mytheme" rel="stylesheet" media="screen, print" href="#">
      <link id="myskin" rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/skins/skin-master.css')); ?>">
       <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style.css')); ?>">
               <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/style-ar.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/reactions/reactions.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/fullcalendar/fullcalendar.bundle.css')); ?>">
      <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/miscellaneous/jqvmap/jqvmap.bundle.css')); ?>">  
      
       
       
          <!-- DEMO related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-brands.css')); ?>">
        <!-- page related CSS below -->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

    <style>
        th{
            width:135px!important;
        }
    </style>
       
       
      
    <?php endif; ?>   
       
           <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/notify.css')); ?>">  

   </head>

    

<?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/layouts/header.blade.php ENDPATH**/ ?>