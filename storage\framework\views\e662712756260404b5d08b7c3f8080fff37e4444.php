<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\AssemblyProducts;
use App\Models\SubImages;
use App\Models\AdditionalProducts;
use App\Models\ProductsVira;
use App\Models\ProductTypeDefault;
use App\Models\VAProducts;
use App\Models\VAPrice;
use App\Models\VAQty;
$Types=ProductTypeDefault::all();
?>
<style>
.long-barcode {
    max-width: 300px !important;
}
</style>
  <title><?php echo e(trans('admin.Products_Sechdule')); ?></title>


     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.Products_Sechdule')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                    </ol>
                                          <style>


       .Hei{
          background:#886ab5;
           color:WHITE;

           padding:10px;
        margin-bottom:20px;
        box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .wei{
                background:#886ab5;
           color:WHITE;

           padding:10px;
           margin-bottom:20px;
           box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .BlogsH{
           display:flex;
           text-align:center;
               flex-direction: column;
       }
   </style>


      <p class="container text-center " style="margin-bottom:10px;">
  <a class="btn btn-primary" data-toggle="collapse" href="#multiCollapseExample1" role="button" aria-expanded="true" aria-controls="multiCollapseExample1"> Size Of Special Offers</a>
  <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#multiCollapseExample2" aria-expanded="false" aria-controls="multiCollapseExample2"> Size Of New Arrival</button>
  <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#multiCollapseExample3" aria-expanded="false" aria-controls="multiCollapseExample3"> Size Of Best Sellers</button>
</p>
<div class="row">
  <div class="col">
    <div class="collapse " id="multiCollapseExample1">
      <div class="card card-body">
            <div  class="col-lg-12 text-center Hei"><h2 class="BlogsH"> Special Offers</h2>width:355px^Height:250px</div>
      </div>
    </div>
  </div>
  <div class="col">
    <div class="collapse " id="multiCollapseExample2">
      <div class="card card-body">
                <div class="col-lg-12 text-center wei"><h2 class="BlogsH">New Arrival</h2>width:450px^Height:450px</div>
      </div>
    </div>
  </div>
   <div class="col">
    <div class="collapse " id="multiCollapseExample3">
      <div class="card card-body">
                 <div class="col-lg-12 text-center wei"><h2 class="BlogsH">Best Sellers</h2>width:306px^Height:306px</div>
      </div>
    </div>
  </div>
</div>

                    <!-- data entry -->
             <form action="<?php echo e(url('FilterPSechdule')); ?>" method="get">
                      <div class="row">

                           <div class="form-group col-md-2">
                        <label><?php echo e(trans('admin.Name')); ?></label>
                    <input type="text" class="form-control" name="Name">
                          </div>

                                    <div class="form-group col-md-2">
                        <label><?php echo e(trans('admin.Code')); ?></label>
                    <input type="text" class="form-control" name="Code">
                          </div>

                        <div class="form-group col-md-2">
                                       <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?> </label>
                                       <select class="select2 form-control w-100" name="Brand">
                                          <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                          <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                          <option value="<?php echo e($brand->id); ?>">
                                 <?php echo e(app()->getLocale() == 'ar' ? ($brand->Name ?? 'غير محدد') : ($brand->NameEn ?? 'Not Specified')); ?>

                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                            <div class="form-group col-md-3">
                    <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label>
                                       <select class="select2 form-control w-100" name="Group" >
                                          <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                          <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                 <option value="<?php echo e($group->id); ?>">
                     <?php echo e(app()->getLocale() == 'ar' ? ($group->Name ?? 'غير محدد') : ($group->NameEn ?? 'Not Specified')); ?>

                                           </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>
                             <div class="form-group col-md-2">
            <label class="form-label" for="">
                                       <?php echo e(trans('admin.Product_Type')); ?>

                                       </label><span class="strick">*</span>
                    <select class="select2 form-control w-100" name="P_Type">
                                          <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
                        <?php $__currentLoopData = $Types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <option value="<?php echo e($type->Type); ?>">
                          <?php if($type->Type == "Completed"): ?>
                                                        <?php echo e(trans('admin.Completed')); ?>

                                                    <?php elseif($type->Type == "Raw"): ?>
                                                         <?php echo e(trans('admin.Raw')); ?>

                                                    <?php elseif($type->Type == "Service"): ?>
                                                         <?php echo e(trans('admin.Service')); ?>

                                                    <?php elseif($type->Type == "Assembly"): ?>
                                                         <?php echo e(trans('admin.Assembly')); ?>

                                                    <?php elseif($type->Type == "Industrial"): ?>
                                                         <?php echo e(trans('admin.Industrial')); ?>

                                                    <?php elseif($type->Type == "Single_Variable"): ?>
                                                         <?php echo e(trans('admin.Single_Variable')); ?>

                                                    <?php elseif($type->Type == "Duble_Variable"): ?>
                                                         <?php echo e(trans('admin.Duble_Variable')); ?>

                                                    <?php elseif($type->Type == "Subscribe"): ?>
                                                        <?php echo e(trans('admin.Subscribe')); ?>

                                                    <?php elseif($type->Type == "Serial"): ?>
                                                         <?php echo e(trans('admin.Serial')); ?>

                         <?php elseif($type->Type == "Petrol"): ?>
                                                         <?php echo e(trans('admin.Petrol')); ?>

                         <?php elseif($type->Type == "Variable_Aggregate"): ?>
                                                         <?php echo e(trans('admin.Variable_Aggregate')); ?>

                         <?php elseif($type->Type == "Additions"): ?>
                                                         <?php echo e(trans('admin.Additions')); ?>

                                                    <?php endif; ?>


                        </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>

                 <div class="form-group col-md-1">
                    <button type="submit" class="btn btn-default" style="margin-top: 25px;"><i class="fal fa-search"></i></button>
                            </div>

                        </div>
                    </form>
                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                    <?php echo e(trans('admin.Products_Sechdule')); ?>

                                    </h2>

                                    <div class="panel-toolbar">

                             <a href="<?php echo e('ExportAllProducts'); ?>" class="btn btn-success btn-sm">
                                 Export Excel
                                            </a>


                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                                                     <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <div class="panel-content">

                                        <!-- datatable start -->
                                        <div id="mobile-overflow">
                                        <table
                                            class="table table-bordered table-hover table-striped mobile-width w-100">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.P_Type')); ?></th>
                                                    <th><?php echo e(trans('admin.Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Units')); ?></th>
                                                    <th><?php echo e(trans('admin.Group')); ?></th>
                                                    <th><?php echo e(trans('admin.Image')); ?></th>
                                                    <th><?php echo e(trans('admin.Image2')); ?></th>
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Additions')); ?></th>
                                                    <th><?php echo e(trans('admin.Status')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                    <?php if($item->P_Type == "Completed"): ?>
                                                        <?php echo e(trans('admin.Completed')); ?>

                                                    <?php elseif($item->P_Type == "Raw"): ?>
                                                         <?php echo e(trans('admin.Raw')); ?>

                                                    <?php elseif($item->P_Type == "Service"): ?>
                                                         <?php echo e(trans('admin.Service')); ?>

                                                    <?php elseif($item->P_Type == "Assembly"): ?>



                                                  <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Assembly<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Assembly')); ?>

                                                        </button>


                                                    <?php elseif($item->P_Type == "Industrial"): ?>
                                                         <?php echo e(trans('admin.Industrial')); ?>

                                                    <?php elseif($item->P_Type == "Single_Variable"): ?>
                                                         <?php echo e(trans('admin.Single_Variable')); ?>

                                                    <?php elseif($item->P_Type == "Duble_Variable"): ?>
                                                         <?php echo e(trans('admin.Duble_Variable')); ?>

                                                    <?php elseif($item->P_Type == "Subscribe"): ?>
                                                        <?php echo e(trans('admin.Subscribe')); ?>

                                                    <?php elseif($item->P_Type == "Serial"): ?>
                                                         <?php echo e(trans('admin.Serial')); ?>

                                                           <?php elseif($item->P_Type == "Petroll"): ?>
                                                         <?php echo e(trans('admin.Petroll')); ?>

                                                        <?php elseif($item->P_Type == "Variable_Aggregate"): ?>


                                                  <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Variable_Aggregate<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Variable_Aggregate')); ?>

                                                        </button>


                                                        <?php elseif($item->P_Type == "Additions"): ?>
                                                         <?php echo e(trans('admin.Additions')); ?>

                                                    <?php endif; ?>
                                                    </td>

                                                    <td>
                                                 <?php echo e(app()->getLocale() == 'ar' ?$item->P_Ar_Name :$item->P_En_Name); ?>

                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Units<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Units')); ?>

                                                        </button>
                                                    </td>
                                                    <td>
                                     <?php if($item->Group()->first()): ?>
                                         <?php echo e(app()->getLocale() == 'ar' ? $item->Group()->first()->Name : $item->Group()->first()->NameEn); ?>

                                     <?php else: ?>
                                         <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                     <?php endif; ?>
                                                    </td>

                                                    <td>
                                                    <?php if(!empty($item->Image)): ?>
                                                    <img src="<?php echo e(URL::to($item->Image)); ?>" class="img-table" >
                                                    <?php else: ?>
                                            <img src="<?php echo e(asset('Admin/img/default.jpeg')); ?>" class="img-table" >
                                                    <?php endif; ?>
                                                    </td>
                                                    <td>
                                                    <?php if(!empty($item->Image2)): ?>
                                                    <img src="<?php echo e(URL::to($item->Image2)); ?>" class="img-table" >
                                                    <?php else: ?>
                                            <img src="<?php echo e(asset('Admin/img/default.jpeg')); ?>" class="img-table" >
                                                    <?php endif; ?>
                                                    </td>

                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Details<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Details')); ?>

                                                        </button>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Additions<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Additions')); ?>

                                                        </button>
                                                    </td>
                                                    <td>
                                                                     <?php if($item->Status == 0): ?>
                 <a href="<?php echo e(url('UnActiveItem/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-check"></i></a>
                            <?php elseif($item->Status == 1): ?>

                 <a href="<?php echo e(url('ActiveItem/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-times"></i></a>
                                <?php endif; ?>

                                                    </td>

                                                    <td class="text-center">

                                       <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('حذف صنف')): ?>
                      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center<?php echo e($item->id); ?>"><i class="fal fa-trash"></i></button>
                                          <?php endif; ?>

                                <?php if($item->P_Type != "Variable_Aggregate"): ?>

                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل صنف')): ?>
                 <a href="<?php echo e(url('EditItems/'.$item->id)); ?>" class="btn btn-default" ><i class="fal fa-edit"></i></a>
                            <?php endif; ?>

                                    <?php else: ?>


                                          <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('تعديل صنف')): ?>
                      <button type="button" class="btn btn-default" data-toggle="modal" data-target="#EDITAV<?php echo e($item->id); ?>"><i class="fal fa-edit"></i></button>
                                          <?php endif; ?>

                            <?php endif; ?>



                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                     <th><?php echo e(trans('admin.P_Type')); ?></th>
                                                    <th><?php echo e(trans('admin.Name')); ?></th>
                                                    <th><?php echo e(trans('admin.Units')); ?></th>
                                                    <th><?php echo e(trans('admin.Group')); ?></th>
                                                    <th><?php echo e(trans('admin.Image')); ?></th>
                                                    <th><?php echo e(trans('admin.Image2')); ?></th>
                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Status')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        <?php echo e($items->Links()); ?>

                                        </div>
                                        <!-- datatable end -->

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

  <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                  <!-- Modal Delete -->
                      <div class="modal fade" id="default-example-modal-center<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                         <?php echo e(trans('admin.RUSWDT')); ?> <strong>

                        <?php echo e(app()->getLocale() == 'ar' ?$item->P_Ar_Name :$item->P_En_Name); ?>

                                        </strong>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.No')); ?></button>
                                    <a href="<?php echo e(url('DeleteItem/'.$item->id)); ?>" class="btn btn-primary"><?php echo e(trans('admin.Yes')); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- Modal Details -->
     <div class="modal fade" id="Details<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Details')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                        <div class="mt-3">
                            <div style="overflow:auto">
                            <table id="" class="table table-bordered table-hover table-striped " >
                            <thead>
                                <tr>



                                    <th><?php echo e(trans('admin.Brand')); ?> </th>
                                    <th><?php echo e(trans('admin.Minimum')); ?> </th>
                                    <th><?php echo e(trans('admin.Maximum')); ?> </th>
                                    <th><?php echo e(trans('admin.If_Offer')); ?> </th>
                                    <th><?php echo e(trans('admin.OfferPrice')); ?> </th>
                                    <th><?php echo e(trans('admin.Length')); ?> </th>
                                    <th><?php echo e(trans('admin.Width')); ?> </th>
                                    <th><?php echo e(trans('admin.Height')); ?> </th>
                                    <th><?php echo e(trans('admin.Weight')); ?> </th>
                                    <th><?php echo e(trans('admin.Saller_Point')); ?> </th>
                                    <th><?php echo e(trans('admin.Customer_Point')); ?> </th>
                                    <th><?php echo e(trans('admin.Sub_Cost')); ?> </th>
                                    <th><?php echo e(trans('admin.Subscribe_Type')); ?> </th>
                                    <th><?php echo e(trans('admin.Validity')); ?> </th>
                                    <th><?php echo e(trans('admin.Days_Notify')); ?> </th>
                                    <th><?php echo e(trans('admin.Store_Show')); ?> </th>
                                    <th><?php echo e(trans('admin.Show_Other_Store')); ?> </th>
                                    <th><?php echo e(trans('admin.Store_Type')); ?> </th>
                                    <th><?php echo e(trans('admin.Brief_Desc')); ?> </th>
                                    <th><?php echo e(trans('admin.Desc')); ?> </th>
                                    <th><?php echo e(trans('admin.Spec')); ?> </th>
                                    <th><?php echo e(trans('admin.Sub_Images')); ?> </th>
                                     <th><?php echo e(trans('admin.P_Type_Details')); ?></th>
                                     <th><?php echo e(trans('admin.Tax')); ?></th>
                                     <th><?php echo e(trans('admin.Code_Typeee')); ?></th>
                                     <th><?php echo e(trans('admin.World_Code')); ?></th>
                                     <th><?php echo e(trans('admin.Origin_Number')); ?></th>
                                     <th><?php echo e(trans('admin.Origin_Country')); ?></th>
                                     <th><?php echo e(trans('admin.SearchCode1')); ?></th>
                                     <th><?php echo e(trans('admin.SearchCode2')); ?></th>
                                     <th><?php echo e(trans('admin.Space')); ?></th>
                                     <th><?php echo e(trans('admin.Storage')); ?></th>
                                     <th><?php echo e(trans('admin.Processor')); ?></th>
                                     <th><?php echo e(trans('admin.Camera')); ?></th>
                                     <th><?php echo e(trans('admin.Screen')); ?></th>
                                     <th><?php echo e(trans('admin.OS')); ?></th>
                                     <th><?php echo e(trans('admin.Battery')); ?></th>
                                     <th><?php echo e(trans('admin.Warranty')); ?></th>
                                     <th><?php echo e(trans('admin.Color')); ?></th>
                                     <th><?php echo e(trans('admin.Category')); ?></th>
                                     <th><?php echo e(trans('admin.Model')); ?></th>
                                     <th><?php echo e(trans('admin.Guess_Price')); ?></th>
                                     <th><?php echo e(trans('admin.Offer_Start_Date')); ?></th>
                                     <th><?php echo e(trans('admin.Offer_End_Date')); ?></th>
                                     <th><?php echo e(trans('admin.Maximum_Sales_Qty')); ?></th>
                                    <th>Cas No </th>
                                    <th>HSN </th>
                                    <th>Uni Code</th>

                                </tr>
                            </thead>
                            <tbody id="">
                                <tr>

                                    <td>
                                    <?php if(!empty($item->Brand) && $item->Brand()->first()): ?>
                               <?php echo e(app()->getLocale() == 'ar' ? $item->Brand()->first()->Name : $item->Brand()->first()->NameEn); ?>

                                    <?php else: ?>
                                        <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                    <?php endif; ?>
                                    </td>
                                     <td><?php echo e($item->Minimum); ?></td>
                                     <td><?php echo e($item->Maximum); ?></td>
                                     <td>
                                    <?php if($item->Offer == 1): ?>
                                         <?php echo e(trans('admin.Yes')); ?>

                                    <?php else: ?>
                                            <?php echo e(trans('admin.No')); ?>

                                    <?php endif; ?>
                                    </td>
                                     <td><?php echo e($item->OfferPrice); ?></td>
                                     <td><?php echo e($item->Length); ?></td>
                                     <td><?php echo e($item->Width); ?></td>
                                     <td><?php echo e($item->Height); ?></td>
                                     <td><?php echo e($item->Weight); ?></td>

                                     <td><?php echo e($item->Saller_Point); ?></td>
                                     <td><?php echo e($item->Customer_Point); ?></td>
                                     <td><?php echo e($item->Sub_Cost); ?></td>
                                     <td>
                                         <?php if(!empty($item->subscribe_type) && $item->subscribe_type()->first()): ?>
                                         <?php echo e($item->subscribe_type()->first()->Name); ?>

                                         <?php else: ?>
                                         <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                         <?php endif; ?>
                                    </td>
                                     <td>
                                    <?php if($item->Validity == 0): ?>
                                     <?php echo e(trans('admin.NO')); ?>

                                    <?php else: ?>
                                      <?php echo e(trans('admin.Yes')); ?>

                                    <?php endif; ?>
                                     </td>
                                     <td>
                                       <?php if($item->Validity == 0): ?>
                                     <?php echo e(trans('admin.No_Found')); ?>

                                    <?php else: ?>
                                     <?php echo e($item->Days_Notify); ?>

                                    <?php endif; ?>

                                    </td>
                                     <td>
                                      <?php if($item->Store_Show == 0): ?>
                                     <?php echo e(trans('admin.NO')); ?>

                                    <?php else: ?>
                                      <?php echo e(trans('admin.Yes')); ?>

                                    <?php endif; ?>

                                    </td>
                                    <td>
                                      <?php if($item->Show_Other_Store == 0): ?>
                                     <?php echo e(trans('admin.NO')); ?>

                                    <?php else: ?>
                                      <?php echo e(trans('admin.Yes')); ?>

                                    <?php endif; ?>

                                    </td>
                                     <td>
                                        <?php if($item->Store_Show == 0): ?>
                                      <?php echo e(trans('admin.No_Found')); ?>

                                    <?php else: ?>

                                          <?php if($item->Store_Type == 0): ?>
                                          <?php echo e(trans('admin.Recently')); ?>

                                          <?php elseif($item->Store_Type == 1): ?>
                                          <?php echo e(trans('admin.Special')); ?>

                                          <?php elseif($item->Store_Type == 2): ?>
                                          <?php echo e(trans('admin.Finaly')); ?>

                                         <?php endif; ?>

                                    <?php endif; ?>
                                    </td>
                                    <td>

                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Brief_Desc<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Brief_Desc')); ?>

                                                        </button>
                                    </td>
                                    <td>

                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Desc<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Desc')); ?>

                                                        </button>
                                    </td>
                                           <td>

                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Spec<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Spec')); ?>

                                                        </button>
                                    </td>

                                            <td>

                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Sub_Images<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Sub_Images')); ?>

                                                        </button>
                                    </td>
                                     <td>

                                                    <?php if($item->P_Type == "Assembly"): ?>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Assembly<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.P_Type_Details')); ?>

                                                        </button>
                                 <?php elseif($item->P_Type == "Single_Variable" or $item->P_Type == "Duble_Variable" ): ?>
                <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Virable<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.P_Type_Details')); ?>

                                                        </button>
                                <?php elseif($item->P_Type == "Serial"): ?>
                                             <button type="button" class="btn btn-default" data-toggle="modal" data-target="#Serial<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.P_Type_Details')); ?>

                                                        </button>

                                                    <?php else: ?>
                                                     <?php echo e(trans('admin.No_Found')); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if(!empty($item->Tax) && $item->Tax()->first()): ?>
                                                        <?php echo e($item->Tax()->first()->Name); ?>

                                                        <?php else: ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                      <?php endif; ?>
                                                    </td>
         <td><?php echo e($item->Code_Type); ?></td>
         <td><?php echo e($item->World_Code); ?></td>
         <td><?php echo e($item->Origin_Number); ?></td>
         <td><?php echo e($item->Origin_Country); ?></td>
         <td><?php echo e($item->SearchCode1); ?></td>
         <td><?php echo e($item->SearchCode2); ?></td>
         <td><?php echo e($item->Space); ?></td>
         <td><?php echo e($item->Storage); ?></td>
         <td><?php echo e($item->Processor); ?></td>
         <td><?php echo e($item->Camera); ?></td>
         <td><?php echo e($item->Screen); ?></td>
         <td><?php echo e($item->OS); ?></td>
         <td><?php echo e($item->Battery); ?></td>
         <td><?php echo e($item->Warranty); ?></td>
         <td><?php echo e($item->Color); ?></td>
         <td><?php echo e($item->Category); ?></td>
         <td><?php echo e($item->Model); ?></td>
         <td><?php echo e($item->Guess_Price); ?></td>
         <td><?php echo e($item->Offer_Start_Date); ?></td>
         <td><?php echo e($item->Offer_End_Date); ?></td>
         <td><?php echo e($item->Maximum_Sales_Qty); ?></td>

         <td><?php echo e($item->Cas_No); ?></td>
                                     <td><?php echo e($item->HSN); ?></td>
                                     <td><?php echo e($item->Uni_Code); ?></td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>


    <!-- Modal Brief_Desc -->
         <div class="modal fade" id="Brief_Desc<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">

                     <?php
                  if(app()->getLocale() == 'ar'){

                  echo html_entity_decode($item->Arabic_Brief_Desc);
                  }else{
                   echo html_entity_decode($item->English_Brief_Desc) ;

                }

                    ?>


                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>
    <!-- Modal Desc -->
         <div class="modal fade" id="Desc<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">

                     <?php
                  if(app()->getLocale() == 'ar'){

                  echo html_entity_decode($item->Ar_Desc);
                  }else{
                   echo html_entity_decode($item->En_Desc) ;

                }

                    ?>


                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>


   <!-- Modal Spec -->
         <div class="modal fade" id="Spec<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">

                     <?php
                  if(app()->getLocale() == 'ar'){

                  echo html_entity_decode($item->Ar_Spec);
                  }else{
                   echo html_entity_decode($item->En_Spec) ;

                }

                    ?>


                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>



   <!-- Modal Sub_Images -->
         <div class="modal fade" id="Sub_Images<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">

                     <?php
          $subs=SubImages::where('Product',$item->id)->get();
                    ?>

                                <div class="row">
                                <?php $__currentLoopData = $subs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-12">
                    <img src="<?php echo e(Storage::url($sub->Image)); ?>" class="img-fluid">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                </div>
                            </div>
                        </div>
                    </div>


                  <!-- Modal Units -->
                <div class="modal fade" id="Units<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Units')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                        <div id="mobile-overflow" class="mt-3">
                            <table id="" class="table table-bordered table-hover table-striped mobile-width">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Rate')); ?> </th>
                                    <th><?php echo e(trans('admin.Barcode')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Price_Two')); ?> </th>
                                    <th><?php echo e(trans('admin.Price_Three')); ?> </th>
                                    <th><?php echo e(trans('admin.Barcode')); ?> </th>
                                    <th><?php echo e(trans('admin.QrCode')); ?> </th>
                                </tr>
                            </thead>
                                    <?php

                                $Units=ProductUnits::where('Product',$item->id)->get();
                                    ?>
                            <tbody id="">

                            <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                      <?php
                                    $x=$uni->Barcode;
                                    $y=DNS1D::getBarcodePNG($x, 'C39');
                                    $z=DNS2D::getBarcodePNG("$x", 'QRCODE',100,100);


                                    ?>
                                <tr>
                                    <td>
                                <?php if($uni->Unit()->first()): ?>
                                    <?php echo e(app()->getLocale() == 'ar' ? $uni->Unit()->first()->Name : $uni->Unit()->first()->NameEn); ?>

                                <?php else: ?>
                                    <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                <?php endif; ?>
                                    </td>
                                     <td><?php echo e($uni->Rate); ?></td>
                                     <td><?php echo e($uni->Barcode); ?></td>
                                     <td><?php echo e($uni->Price); ?></td>
                                     <td><?php echo e($uni->Price_Two); ?></td>
                                     <td><?php echo e($uni->Price_Three); ?></td>

                                     <td>
                                         <a href="data:image/png;base64,<?php echo e($y); ?>" download>     <img src="data:image/png;base64,<?php echo e($y); ?>" alt="barcode"   class="long-barcode" />  </a>
                                    </td>
                                     <td>
                                         <a href="data:image/png;base64,<?php echo e($z); ?>" download>       <img src="data:image/png;base64,<?php echo e($z); ?>" width="100%" height="100%" alt="QR" />  </a>
                                    </td>
                                </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Rate')); ?> </th>
                                    <th><?php echo e(trans('admin.Barcode')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Price_Two')); ?> </th>
                                    <th><?php echo e(trans('admin.Price_Three')); ?> </th>
                                    <th><?php echo e(trans('admin.Barcode')); ?> </th>
                                    <th><?php echo e(trans('admin.QrCode')); ?> </th>
                                </tr>
                            </tfoot>
                        </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>


       <!-- Modal Assembly -->
                <div class="modal fade" id="Assembly<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Assembly')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                        <div class="mt-3">
                            <table id="" class="table table-bordered table-hover table-striped ">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.P_Ar_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.P_En_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.P_Code')); ?> </th>
                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Total')); ?> </th>
                                </tr>
                            </thead>
                                    <?php

                                $Assembls=AssemblyProducts::where('p_id',$item->id)->get();
                                    ?>
                            <tbody id="">

                            <?php $__currentLoopData = $Assembls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($asi->P_Ar_Name); ?></td>
                                     <td><?php echo e($asi->P_En_Name); ?></td>
                                     <td><?php echo e($asi->P_Code); ?></td>
                                     <td>
                                  <?php if($asi->Unit()->first()): ?>
                                      <?php echo e(app()->getLocale() == 'ar' ? $asi->Unit()->first()->Name : $asi->Unit()->first()->NameEn); ?>

                                  <?php else: ?>
                                      <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                  <?php endif; ?>
                                    </td>
                                     <td><?php echo e($asi->Qty); ?></td>
                                     <td><?php echo e($asi->Price); ?></td>
                                     <td><?php echo e($asi->Total); ?></td>
                                </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                              <th><?php echo e(trans('admin.P_Ar_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.P_En_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.P_Code')); ?> </th>
                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.Total')); ?> </th>
                                </tr>
                            </tfoot>
                        </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>


     <!-- Modal Virable -->
                <div class="modal fade" id="Virable<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Duble_Variable')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                        <div class="mt-3">
                                   <form action="<?php echo e(url('MultiDeleteVira')); ?>" method="post">
                           <?php echo csrf_field(); ?>

                            <table id="" class="table table-bordered table-hover table-striped">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Virable')); ?> </th>
                                   <?php if($item->P_Type == "Duble_Variable"): ?>
                                    <th><?php echo e(trans('admin.Virable')); ?> </th>
                                    <?php endif; ?>
                                    <th>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fal fa-trash"></i>
                                        </button>
                                    </th>
                                </tr>
                            </thead>
                                    <?php
                                $Viraas=ProductsVira::where('Product',$item->id)->get();
                                    ?>
                            <tbody id="">

                            <?php $__currentLoopData = $Viraas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $v): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                    <?php if(!empty($v->V1) && $v->V1()->first()): ?>
                                   <?php echo e(app()->getLocale() == 'ar' ? $v->V1()->first()->Name : $v->V1()->first()->NameEn); ?>

                                    <?php else: ?>
                                        <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                    <?php endif; ?>
                                    </td>
                                  <?php if($item->P_Type == "Duble_Variable"): ?>
                                    <td>
                                        <?php if(!empty($v->V2) && $v->V2()->first()): ?>
                         <?php echo e(app()->getLocale() == 'ar' ? $v->V2()->first()->Name : $v->V2()->first()->NameEn); ?>

                                        <?php else: ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                        <?php endif; ?>
                                    </td>
                                <?php endif; ?>
                                    <td>
                            <input type="checkbox" name="Vira[]" value="<?php echo e($v->id); ?>">
                                    </td>
                                </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                <th><?php echo e(trans('admin.Virable')); ?> </th>
                                     <?php if($item->P_Type == "Duble_Variable"): ?>
                                    <th><?php echo e(trans('admin.Virable')); ?> </th>
                                    <?php endif; ?>
                                </tr>
                            </tfoot>
                        </table>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>

     <!-- Serial -->
           <div class="modal fade" id="Serial<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Serial')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">

                    <div class="row">
                        <select class="select2 form-control" id="store">
                        <option value=""><?php echo e(trans('admin.Store')); ?></option>
                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <option value="<?php echo e($store->id); ?>">
                       <?php echo e(app()->getLocale() == 'ar' ? ($store->Name ?? 'غير محدد') : ($store->NameEn ?? 'Not Specified')); ?>

                            </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <input type="hidden" id="product" value="<?php echo e($item->id); ?>">
                            </div>

                <form action="<?php echo e(url('EditSerialQty')); ?>" method="get">
                        <div class="row mt-3">

                            <table id="" class="table table-bordered table-hover table-striped w-100">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Code')); ?>

                                         <button type="submit" class="btn btn-success">
                                <i class="fal fa-edit"></i>
                                        </button>
                                    </th>
                                    <th>
                             <?php echo e(trans('admin.Actions')); ?>

                                    </th>
                                </tr>
                            </thead>
                            <tbody class="SERIAL">

                            </tbody>
                        </table>
                        </div>
                       </form>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>


  <!-- Additions -->
           <div class="modal fade" id="Additions<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Additions')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                           <?php   $Additions=AdditionalProducts::where('Product',$item->id)->get();  ?>
                                <div class="modal-body">
           <?php if($item->P_Type != "Variable_Aggregate"): ?>



                        <div class="row">

                        <?php $__currentLoopData = $Additions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $add): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-12">
                            <?php if(!empty($add->Additional_Product)): ?>
                      <?php echo e(app()->getLocale() == 'ar' ?$add->Additional_Product()->first()->P_Ar_Name :$add->Additional_Product()->first()->P_En_Name); ?>

                            <?php endif; ?>
                            </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </div>


                     <?php else: ?>

                        <form action="<?php echo e(url('PostEditAddittionAV')); ?>" method="post">
                            <?php echo csrf_field(); ?>

                        <input type="hidden" name="ID" value="<?php echo e($item->id); ?>">
                                   <div class="form-row">

                                    <div class="form-group col-md-8">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Product')); ?> </label>
                       <select class="select2 form-control w-100"  id="AdditionProduct<?php echo e($item->id); ?>" onchange="AdditionPlus(<?php echo e($item->id); ?>)">
                               <option value=""><?php echo e(trans('admin.Product')); ?></option>

                           <?php $__currentLoopData = $AdditionsProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $addd): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                  <option value="<?php echo e($addd->id); ?>"><?php echo e(app()->getLocale() == 'ar' ?$addd->P_Ar_Name :$addd->P_En_Name); ?></option>
                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                       </select>
                                    </div>



                                            <div class="form-group col-md-4">
                            <button type="button" onclick="InsertAddition(<?php echo e($item->id); ?>)" class="btn btn-default" id="addAdditionP<?php echo e($item->id); ?>" style="display: none"><i class="fal fa-plus"></i></button>
                                     </div>


           <!-- datatable start -->
                                                                <div id="mobile-overflow">
                                                                <table id=""
                                                                    class="table table-bordered table-hover table-striped w-100 mobile-width">
                                                                    <thead>
                                                                        <tr>
                                                                        <th><?php echo e(trans('admin.Product')); ?></th>
                                                                        <th><?php echo e(trans('admin.Actions')); ?></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="dataAddition<?php echo e($item->id); ?>">

                                                        <?php $__currentLoopData = $Additions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $de): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                                    <tr>
                                                <td>
                                         <?php if(!empty($de->Additional_Product)): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ?$de->Additional_Product()->first()->P_Ar_Name :$de->Additional_Product()->first()->P_En_Name); ?>


                                          <input type="hidden" name="Additional_Product[]" value="<?php echo e($de->Additional_Product); ?>">
                                      <?php endif; ?>


                                                </td>
                                                <td>
                                    <button  onclick="dleoAV(<?php echo e($item->id); ?>)" id="DelAV<?php echo e($item->id); ?>" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                                </td>
                                                </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </tbody>

                                                                </table>
                                                                </div>
                                                                <!-- datatable end -->



                                 </div>
                              <div class="form-row">
                                <button type="submit" class="btn btn-success"><i class="fal fa-edit"></i></button>
                            </div>
                                    </form>
                <?php endif; ?>








                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>


               <!-- Modal Variable_Aggregate -->
         <div class="modal fade" id="Variable_Aggregate<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Units')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                        <div id="mobile-overflow" class="mt-3">
                            <table id="" class="table table-bordered table-hover table-striped mobile-width">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Product_Code')); ?> </th>
                                    <th><?php echo e(trans('admin.P_Ar_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.P_En_Name')); ?> </th>
                                    <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.Actions')); ?> </th>

                                </tr>
                            </thead>
                                    <?php

                                $VAs=VAProducts::where('Product',$item->id)->get();
                                    ?>
                            <tbody id="">

                            <?php $__currentLoopData = $VAs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $va): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <?php   $VAQtys=VAQty::where('Product',$item->id)->where('VAProductID',$va->id)->get();   ?>
                                <tr>
                                     <td><?php echo e($va->Product_Code); ?></td>
                                     <td><?php echo e($va->P_Ar_Name); ?></td>
                                     <td><?php echo e($va->P_En_Name); ?></td>
                                    <td>
                                <?php if($va->Unit()->first()): ?>
                                    <?php echo e(app()->getLocale() == 'ar' ? $va->Unit()->first()->Name : $va->Unit()->first()->NameEn); ?>

                                <?php else: ?>
                                    <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                <?php endif; ?>
                                    </td>

                                         <td> <a class="btn btn-danger" href="<?php echo e(url('DeleteVAPro/'.$va->id)); ?>"><i class="fal fa-trash"></i></a></td>
                                </tr>
                            <?php $__currentLoopData = $VAQtys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $Qva): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                                   <td>
                                            <?php if(!empty($Qva->MainV) && $Qva->MainV()->first()): ?>
                                <?php echo e(app()->getLocale() == 'ar' ? $Qva->MainV()->first()->Name : $Qva->MainV()->first()->NameEn); ?>

                                            <?php else: ?>
                                                <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                            <?php endif; ?>
                                    </td>

                                                    <td>
                                                             <?php if(!empty($Qva->SubV) && $Qva->SubV()->first()): ?>
                                <?php echo e(app()->getLocale() == 'ar' ? $Qva->SubV()->first()->Name : $Qva->SubV()->first()->NameEn); ?>

                                                        <?php else: ?>
                                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                        <?php endif; ?>
                                    </td>
                                     <td><?php echo e($Qva->Qty); ?></td>

                                      <td> <a class="btn btn-danger" href="<?php echo e(url('DeleteVAProQty/'.$Qva->id)); ?>"><i class="fal fa-trash"></i></a></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>

                     <table id="" class="table table-bordered table-hover table-striped mobile-width">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Virable')); ?> </th>
                                    <th><?php echo e(trans('admin.Virable')); ?> </th>
                                    <th><?php echo e(trans('admin.Price')); ?> </th>
                                    <th><?php echo e(trans('admin.OfferPrice')); ?> </th>
                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                </tr>
                            </thead>
                                    <?php

                                $VAPrices=VAPrice::where('Product',$item->id)->get();
                                    ?>
                            <tbody id="">

                            <?php $__currentLoopData = $VAPrices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vaPrice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                <tr>

                                               <td>
                                            <?php if(!empty($vaPrice->MainV) && $vaPrice->MainV()->first()): ?>
                                <?php echo e(app()->getLocale() == 'ar' ? $vaPrice->MainV()->first()->Name : $vaPrice->MainV()->first()->NameEn); ?>

                                            <?php else: ?>
                                                <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                            <?php endif; ?>
                                    </td>

                                                    <td>
                                                             <?php if(!empty($vaPrice->SubV) && $vaPrice->SubV()->first()): ?>
                                <?php echo e(app()->getLocale() == 'ar' ? $vaPrice->SubV()->first()->Name : $vaPrice->SubV()->first()->NameEn); ?>

                                                        <?php else: ?>
                                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                        <?php endif; ?>
                                    </td>
                                     <td><?php echo e($vaPrice->Price); ?></td>
                                     <td><?php echo e($vaPrice->Offer_Price); ?></td>
                                     <td>

                <a class="btn btn-danger" href="<?php echo e(url('DeleteVAPrice/'.$vaPrice->id)); ?>"><i class="fal fa-trash"></i></a>
                                    </td>

                                </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>

                            <h2><?php echo e(trans('admin.AddNew')); ?></h2>
                            <hr style="background: red">


                            <form action="<?php echo e(url('PostEditQTYPriceAV')); ?>" method="post">
                                <?php echo csrf_field(); ?>

                                <input type="hidden" name="ID" value="<?php echo e($item->id); ?>">
                                               <!-- Variable_Aggregate -->
                                    <div id="HideVariable_Aggregate"  class="col-md-12 mb-12">
                                       <div class="form-group col-md-12 mb-12">
                                          <div class="input-items">
        <input style="background: brown;color: white;" type="text" id="searchVariableAggregate<?php echo e($item->id); ?>" onkeypress="SearchProAv(<?php echo e($item->id); ?>)" onkeyup="SearchProAv(<?php echo e($item->id); ?>)" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?> ">
                                        </div>
                                       </div>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                          <thead>
                                             <tr>
                                                <th><?php echo e(trans('admin.Product_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Product_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Unit')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="dataVariableAggregate<?php echo e($item->id); ?>">
                                          </tbody>
                                       </table>
                                       <table class="table table-bordered table-hover table-striped w-100 hide-products-table">
                                          <thead>
                                             <tr>
                                            <th><?php echo e(trans('admin.Product_Name')); ?></th>
                                                <th><?php echo e(trans('admin.Product_Code')); ?></th>
                                                <th><?php echo e(trans('admin.Unit')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data-dt-VariableAggregate<?php echo e($item->id); ?>">


                                          </tbody>
                                       </table>

                                        <div class="row" style="background: darkgray;padding: 15px;">
                                        <div class="col-md-3">
                                    <label><?php echo e(trans('admin.Virable')); ?></label>
                                    <select class=" form-control" id="PriceVASelect<?php echo e($item->id); ?>" onclick="SayPrice(<?php echo e($item->id); ?>)" onchange="SubSayPrice(<?php echo e($item->id); ?>)">

                                            </select>
                                        </div>

                                                   <div class="col-md-3">
                                    <label><?php echo e(trans('admin.Virable')); ?></label>
                                    <select class="select2 form-control" id="PriceSubVASelect<?php echo e($item->id); ?>">

                                            </select>
                                        </div>

                                                                                       <div class="col-md-3">
                                    <label><?php echo e(trans('admin.Price')); ?></label>
                                  <input type="number" step="any" class="form-control" id="PriceVA<?php echo e($item->id); ?>">
                                        </div>

                                            <div class="col-md-3">
                                    <label><?php echo e(trans('admin.Offer_Price')); ?></label>
                                  <input type="number" step="any" class="form-control" id="OfferPriceVA<?php echo e($item->id); ?>">
                                        </div>

                                              <div class="col-md-3">
                                        <button class="btn btn-primary"  onclick="AddPriceVA(<?php echo e($item->id); ?>)" type="button"><i class="fal fa-plus"></i></button>
                                            </div>

                                        </div>

                                              <table class="table table-bordered table-hover table-striped w-100 hide-products-table" style="background: bisque;">
                                          <thead>
                                             <tr>
                                            <th><?php echo e(trans('admin.Virable')); ?></th>
                                                <th><?php echo e(trans('admin.Virable')); ?></th>
                                                <th><?php echo e(trans('admin.Price')); ?></th>
                                                <th><?php echo e(trans('admin.Offer_Price')); ?></th>
                                                <th><?php echo e(trans('admin.Actions')); ?></th>
                                             </tr>
                                          </thead>
                                          <tbody id="data-price-VariableAggregate<?php echo e($item->id); ?>">
                                          </tbody>
                                       </table>


                                    </div>

                               <div class="row">
                        <button type="submit" class="btn btn-success"><i class="fal fa-check"></i></button>
                                </div>
                            </form>


                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        </div>
                    </div>
                </div>
            </div>
            </div>




                  <!-- Modal Edit -->
                      <div class="modal fade" id="EDITAV<?php echo e($item->id); ?>" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title">
                                         <?php echo e(trans('admin.Edit')); ?> <strong>

                        <?php echo e(app()->getLocale() == 'ar' ?$item->P_Ar_Name :$item->P_En_Name); ?>

                                        </strong>
                                    </h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                            <form action="<?php echo e(url('PostEditAV')); ?>" method="post">
                                <?php echo csrf_field(); ?>
                          <input type="hidden" name="ID" value="<?php echo e($item->id); ?>">
                                <div class="modal-body">
                                <div class="row">

                           <div class="form-group col-md-6">
                               <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Ar_Name')); ?>

                                                    </label><span class="strick">*</span>
     <input type="text" name="P_Ar_Name" value="<?php echo e($item->P_Ar_Name); ?>" placeholder="<?php echo e(trans('admin.Product_Ar_Name')); ?> "  class="form-control" required>
                                                        </div>

                                                        <div class="form-group col-md-6">
                             <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Product_En_Name')); ?></label>
 <input type="text" name="P_En_Name" value="<?php echo e($item->P_En_Name); ?>" placeholder="<?php echo e(trans('admin.Product_En_Name')); ?> "  class="form-control">
                                                        </div>

                                                        <div class="form-group col-md-6">
                                    <label class="form-label" for="">  <?php echo e(trans('admin.Brand')); ?> </label>
                                                            <select class="select2 form-control w-100" name="Brand">
                                                                <option value=""><?php echo e(trans('admin.Brand')); ?></option>
                                                                <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
             <option value="<?php echo e($brand->id); ?>" <?php if($item->Brand == $brand->id): ?> selected <?php endif; ?>>
                                                       <?php echo e(app()->getLocale() == 'ar' ? ($brand->Name ?? 'غير محدد') : ($brand->NameEn ?? 'Not Specified')); ?>

                                                                </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>


                                                        <div class="form-group col-md-6">
                  <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                                                <select class="select2 form-control w-100" name="Group" required>
                                       <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                                                <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
     <option value="<?php echo e($group->id); ?>" <?php if($item->Group == $group->id): ?> selected <?php endif; ?>>
                                      <?php echo e(app()->getLocale() == 'ar' ? ($group->Name ?? 'غير محدد') : ($group->NameEn ?? 'Not Specified')); ?>

                                                    </option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>


                          <div class="form-group col-md-2 mb-12">
      <label class="form-label customize-input" for="imgInp"><i class="fal fa-image"></i> <?php echo e(trans('admin.Image')); ?>  </label>
                                            <input type="file" name="Image">


                                        <?php if(!empty($item->Image)): ?>
                                     <img src="<?php echo e(URL::to($item->Image)); ?>" class="control-img">
                                        <?php endif; ?>
                                                        </div>



                                         <div class="form-group col-md-12">
                                             <label class="form-label" for=""><?php echo e(trans('admin.Store_Show')); ?>  </label>
                             <select class="select2 form-control w-100" name="Store_Show" id="Store_Show" onchange="Show()">
                 <option value="0" <?php if($item->Store_Show == 0): ?> selected <?php endif; ?> ><?php echo e(trans('admin.NO')); ?></option>
                 <option value="1" <?php if($item->Store_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Storee')); ?></option>
                 <option value="2" <?php if($item->Store_Show == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Price_List')); ?></option>
                 <option value="3" <?php if($item->Store_Show == 3): ?> selected <?php endif; ?>><?php echo e(trans('admin.Both')); ?></option>
                                                            </select>
                                                        </div>
    <div class="form-group col-md-12">
                                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Ar_Desc')); ?>  </label>
                                                            <textarea class="js-summernote" id="saveToLocal" name="Ar_Desc">
                                                            <?php echo e($item->Ar_Desc); ?>

                                                            </textarea>
                                                        </div>
                                                        <div class="form-group col-md-12">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Desc')); ?>   </label>
                                                     <textarea class="js-summernote" id="saveToLocal" name="En_Desc">
                                                            <?php echo e($item->En_Desc); ?>

                                                            </textarea>
                                                        </div>
                                                        <div class="form-group col-md-12">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Ar_Spec')); ?>   </label>
                                                                 <textarea class="js-summernote" id="saveToLocal" name="Ar_Spec">
                                                            <?php echo e($item->Ar_Spec); ?>

                                                            </textarea>
                                                        </div>
                                                        <div class="form-group col-md-12">
                                                            <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.En_Spec')); ?>   </label>
                                                          <textarea class="js-summernote" id="saveToLocal" name="En_Spec">
                                                            <?php echo e($item->En_Spec); ?>

                                                            </textarea>
                                                        </div>



                                                                 <div class="row">
                                                        <div class="form-group col-md-3 mb-3">
                                           <label class="form-label" for=""> <?php echo e(trans('admin.Sub_Images')); ?></label>

                                            <input type="file" name="SubImage[]" multiple>
                                                        </div>
                                                   <?php    $subs=SubImages::where('Product',$item->id)->get();    ?>

                                                            <?php $__currentLoopData = $subs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-10">
                    <img src="../<?php echo e(Storage::url($sub->Image)); ?>" class="control-img">
                                    </div>
                            <div class="col-md-2">
                            <a href="<?php echo e(url('DelSubImage/'.$sub->id)); ?>" class="btn btn-default"><i class="fal fa-times"></i></a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                  </div>

                                    </div>

                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                                    <button type="submit" class="btn btn-secondary" ><?php echo e(trans('admin.Edit')); ?></button>

                                </div>
                                  </form>
                            </div>
                        </div>
                    </div>



<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>

  <script>
    $(document).ready(function()
    {
        $(function()
        {
            $('.select2').select2();

            $(".select2-placeholder-multiple").select2(
            {
                placeholder: "Select State"
            });
            $(".js-hide-search").select2(
            {
                minimumResultsForSearch: 1 / 0
            });
            $(".js-max-length").select2(
            {
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items"
            });
            $(".select2-placeholder").select2(
            {
                placeholder: "Select a state",
                allowClear: true
            });

            $(".js-select2-icons").select2(
            {
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function(elm)
                {
                    return elm
                }
            });

            function icon(elm)
            {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
            }

            $(".js-data-example-ajax").select2(
            {
                ajax:
                {
                    url: "https://api.github.com/search/repositories",
                    dataType: 'json',
                    delay: 250,
                    data: function(params)
                    {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params)
                    {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination:
                            {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Search for a repository',
                escapeMarkup: function(markup)
                {
                    return markup;
                }, // let our custom formatter work
                minimumInputLength: 1,
                templateResult: formatRepo,
                templateSelection: formatRepoSelection
            });

            function formatRepo(repo)
            {
                if (repo.loading)
                {
                    return repo.text;
                }

                var markup = "<div class='select2-result-repository clearfix d-flex'>" +
                    "<div class='select2-result-repository__avatar mr-2'><img src='" + repo.owner.avatar_url + "' class='width-2 height-2 mt-1 rounded' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title fs-lg fw-500'>" + repo.full_name + "</div>";

                if (repo.description)
                {
                    markup += "<div class='select2-result-repository__description fs-xs opacity-80 mb-1'>" + repo.description + "</div>";
                }

                markup += "<div class='select2-result-repository__statistics d-flex fs-sm'>" +
                    "<div class='select2-result-repository__forks mr-2'><i class='fal fa-lightbulb'></i> " + repo.forks_count + " Forks</div>" +
                    "<div class='select2-result-repository__stargazers mr-2'><i class='fal fa-star'></i> " + repo.stargazers_count + " Stars</div>" +
                    "<div class='select2-result-repository__watchers mr-2'><i class='fal fa-eye'></i> " + repo.watchers_count + " Watchers</div>" +
                    "</div>" +
                    "</div></div>";

                return markup;
            }

            function formatRepoSelection(repo)
            {
                return repo.full_name || repo.text;
            }
        });
    });

</script>
<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });

</script>

<!-- Seria Scripts -->
<script>

   $(document).ready(function(){

    fetch_customer_data();

    function fetch_customer_data(store='',product='')
    {
     $.ajax({
      url:'SerialProductsQtyFilter',
      method:'GET',
      data:{store:store,product:product},
      dataType:'json',
      success:function(data)
      {
       $('.SERIAL').html(data.table_data);
      }
     })
    }


    $(document).on('change', '#store', function(){
     var store = $(this).val();
     var product = $('#product').val();
     fetch_customer_data(store,product);
    });


   });

</script>

<!-- Addition Product Table -->
<script>

      function InsertAddition(r) {
       var AdditionProduct=$('#AdditionProduct'+r).val();
       var AdditionProductName=$('#AdditionProduct'+r+' option:selected').text();




        var table = ` <tr>
                                <td>
                            ${AdditionProductName}
          <input type="hidden" name="Additional_Product[]" value="${AdditionProduct}">
                                </td>


                                <td>
                    <button id="DelDepP${r}" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>

                             </tr>`;

        document.getElementById("dataAddition"+r).innerHTML += table;
      document.getElementById("addAdditionP"+r).style.display = "none";

        $("#dataAddition"+r).on("click", "#DelDepP"+r, function (e) {
            $(this).closest("tr").remove();

        });
    }

    function AdditionPlus(r){
        var AdditionProduct=$('#AdditionProduct'+r).val();

        if (AdditionProduct != "") {
            document.getElementById("addAdditionP"+r).style.display = "block";
        }


         if (AdditionProduct == "") {
             document.getElementById("addAdditionP"+r).style.display = "none";
        }

    }

       function dleoAV(r){

             $("#dataAddition"+r).on("click", "#DelAV"+r, function (e) {
            $(this).closest("tr").remove();

        });



        }


</script>


<!-- Variable_Aggregate -->
<script>
   function SearchProAv(r){

       var searchVariableAggregate=$('#searchVariableAggregate'+r).val();
     $.ajax({
      url:'VariableAggregateFilter',
      method:'GET',
      data:{searchVariableAggregate:searchVariableAggregate,r:r},
      dataType:'json',
      success:function(data)
      {
       $('#dataVariableAggregate'+r).html(data.table_data);
      }
     })




   }
</script>

<!-- Add Variable_Aggregate -->
<script>
   function FunVA(r) {

             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitAssem"+r).val();
             var UnitName = $("#UnitNameAssem"+r).val();
             var Barcode = $("#CodeAssem"+r).val();
             var LANG = $("#LANG").val();
             var RR = $("#RR"+r).val();


             document.getElementById("AddVA"+r).style.display = "none";
             document.getElementById("RowVA"+r).style.display = "none";
          var LANG = $("#LANG").val();
       if(LANG == 'ar' ){
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;
          }

             var markup = "<tr><td><input type='hidden' name='P_Ar_NameVA[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_NameVA[]' value='"+P_En_Name+"'>" + Nemo + "</td><td><input type='hidden' name='P_CodeVA[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='UnitVA[]' value='"+UnitID+"'>" + UnitName + "</td><td>  <button id='DelVA"+r+"' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDVA"+r+"' type='hidden' name='ProductVA[]' value='"+Product+"'></td></tr>";


       if(LANG == 'ar'){
           var VAName='المتغير';
           var VAQty='الكميه';
        }else{
                      var VAName='Virable';
           var VAQty='Qty';

        }
       markup +="<tr id='rowVA"+r+"' style='background: cadetblue;'><td><lable>"+VAName+"</label><select onclick='Say("+r+")' onchange='SubSay("+r+")' class='select2 form-control' id='VASelect"+r+"'></select></td><td><lable>"+VAName+"</label><select  class='select2 form-control' id='VASubSelect"+r+"'></select></td><td><lable>"+VAQty+"</label><input type='number' step='any' id='VAQty"+r+"' class='form-control' value='1'></td><td><button class='btn btn-primary' onclick='AddToVA("+r+")' type='button'><i class='fal fa-plus'></i></button></td></tr><tr id='BodyVA"+r+"'></tr>";






            var  Product_IDInp =$("#Product_IDVA"+r).val();

         if(Product != Product_IDInp){
             $("#data-dt-VariableAggregate"+RR).append(markup);
         }


        $('#data-dt-VariableAggregate').on('click', '#DelVA'+r, function(e){
                $(this).closest('tr').remove();
                $('#rowVA'+r).remove();
                $('#BodyVA'+r).remove();
                    })


     }

    function Say(r){
        $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#VASelect'+r).empty();

                       $.each(data, function(key, value){

             $('#VASelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });



                                   var VA=$('#VASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#VASubSelect'+r).empty();

                       $.each(data, function(key, value){

             $('#VASubSelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });


    }

    function SubSay(r){

                        var VA=$('#VASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#VASubSelect'+r).empty();

                       $.each(data, function(key, value){

             $('#VASubSelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });



    }

    function  AddToVA(r){

         var VA=$('#VASelect'+r).val();
         var VASub=$('#VASubSelect'+r).val();
         var VAName=$('#VASelect'+r+' option:selected').text();
         var VASubName=$('#VASubSelect'+r+' option:selected').text();
         var VAQty=$('#VAQty'+r).val();


       var markup = "<tr style='background: bisque;'><td><input type='hidden' name='ViraVA[]' value='"+VA+"'>" + VAName + "</td><td><input type='hidden' name='SubViraVA[]' value='"+VASub+"'>" + VASubName + "</td><td><input type='hidden' name='QtyVA[]' value='"+VAQty+"'><input type='hidden' name='ProductVAQty[]' value='"+r+"'>" + VAQty + "</td><td><i id='delVA"+r+"' class='fal fa-trash'></i></td></tr>";

          $("#BodyVA"+r).append(markup);



            $("#BodyVA"+r).on("click", "#delVA"+r, function (e) {
            $(this).closest("tr").remove();

        });



    }


    function AddPriceVA(r){


               var PriceVASelect=$('#PriceVASelect'+r).val();
               var PriceSubVASelect=$('#PriceSubVASelect'+r).val();
       var PriceVASelectName=$('#PriceVASelect'+r+' option:selected').text();
       var PriceSubVASelectName=$('#PriceSubVASelect'+r+' option:selected').text();

 var PriceVA=$('#PriceVA'+r).val();
 var OfferPriceVA=$('#OfferPriceVA'+r).val();


        var table = ` <tr>
                                <td>
                            ${PriceVASelectName}
          <input type="hidden" name="PriceVAMainV[]" value="${PriceVASelect}">
                                </td>

                    <td>
                            ${PriceSubVASelectName}
          <input type="hidden" name="PriceVASubV[]" value="${PriceSubVASelect}">
                                </td>


                    <td>
                            ${PriceVA}
          <input type="hidden" name="PriceVA[]" value="${PriceVA}">
                                </td>

                          <td>
                            ${OfferPriceVA}
          <input type="hidden" name="OfferPriceVA[]" value="${PriceVA}">
                                </td>


                                <td>
                    <button id="DelPriceVA${r}" type="button" class="btn btn-default"><i class="fal fa-trash"></i></button>
                                </td>

                             </tr>`;

        document.getElementById("data-price-VariableAggregate"+r).innerHTML += table;


        $("#data-price-VariableAggregate"+r).on("click", "#DelPriceVA"+r, function (e) {
            $(this).closest("tr").remove();

        });

    }

        function SayPrice(r){
        $.ajax({
                   url: 'VASelectFilterr',
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#PriceVASelect'+r).empty();

                       $.each(data, function(key, value){

             $('#PriceVASelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });



                                   var VA=$('#PriceVASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#PriceSubVASelect'+r).empty();

                       $.each(data, function(key, value){

             $('#PriceSubVASelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });


    }

    function SubSayPrice(r){

                        var VA=$('#PriceVASelect'+r).val();
                          $.ajax({
                   url: 'VASubSelectFilterr',
                   type:"GET",
                   data:{
                       VA:VA
                   },
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },

                   success:function(data) {

                       $('#PriceSubVASelect'+r).empty();

                       $.each(data, function(key, value){

             $('#PriceSubVASelect'+r).append('<option value="'+ key +'">' + value + '</option>');

                       });





                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });



    }

</script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/ProductsSechdule.blade.php ENDPATH**/ ?>